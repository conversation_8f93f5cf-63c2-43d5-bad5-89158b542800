import {
  Controller,
  Get,
  Patch,
  Param,
  Body,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiExtraModels,
} from '@nestjs/swagger';
import { JwtEmployeeGuard } from '@modules/auth/guards';
import { CurrentEmployee } from '@modules/auth/decorators';
import { ApiResponseDto } from '@common/response';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions';
import { AdminUserAgentService } from '../services/admin-user-agent.service';
import {
  UserAgentQueryDto,
  UserAgentListItemDto,
  UpdateUserAgentStatusDto,
} from '../dto/user-agent';
import { SWAGGER_API_TAGS } from '@common/swagger';
import { ApiErrorResponse } from '@/common/decorators/api-error-response.decorator';

/**
 * Controller xử lý các API endpoint cho quản lý agent của user (dành cho admin)
 */
@ApiTags(SWAGGER_API_TAGS.ADMIN_AGENT)
@Controller('admin/user-agents')
@UseGuards(JwtEmployeeGuard)
@ApiBearerAuth('JWT-auth')
@ApiExtraModels(UserAgentListItemDto, ApiResponseDto)
export class AdminUserAgentController {
  constructor(private readonly adminUserAgentService: AdminUserAgentService) {}

  /**
   * Lấy danh sách agent của user
   * @param queryDto Tham số truy vấn
   * @returns Danh sách agent với phân trang
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách agent của user' })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách agent thành công',
    schema: ApiResponseDto.getPaginatedSchema(UserAgentListItemDto),
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.AGENT_LIST_FAILED,
  )
  async getAgents(@Query() queryDto: UserAgentQueryDto) {
    const result = await this.adminUserAgentService.findAll(queryDto);
    return ApiResponseDto.paginated(result);
  }
}
