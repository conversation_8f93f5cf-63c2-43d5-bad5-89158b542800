import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsArray,
  IsBoolean,
  IsIn,
  IsNotEmpty,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
  IsUUID,
  Matches,
  Max,
  MaxLength,
  Min,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';
import { IsValidModelConfig, IsValidUserPersonalModel } from '@modules/agent/validators/model-config.validator';

/**
 * DTO cho cấu hình model
 * Model ID và Provider ID đã được chuyển sang entity level
 */
export class ModelConfigDto {

  /**
   * Nhiệt độ (temperature)
   */
  @ApiProperty({
    description: '<PERSON>hi<PERSON>t độ (temperature)',
    example: 0.7,
  })
  @IsNumber()
  @Min(0)
  @Max(2)
  temperature: number;

  /**
   * Top P
   */
  @ApiProperty({
    description: 'Top P',
    example: 0.9,
  })
  @IsNumber()
  @Min(0)
  @Max(1)
  top_p: number;

  /**
   * Top K
   */
  @ApiProperty({
    description: 'Top K',
    example: 40,
  })
  @IsNumber()
  @Min(0)
  top_k: number;

  /**
   * <PERSON><PERSON> token tối đa
   */
  @ApiProperty({
    description: 'Số token tối đa',
    example: 1000,
  })
  @IsNumber()
  @Min(1)
  max_tokens: number;
}

/**
 * DTO cho thông tin profile của agent
 */
export class ProfileDto {
  /**
   * Giới tính
   */
  @ApiProperty({
    description: 'Giới tính',
    example: 'MALE',
  })
  @IsString()
  gender: string;

  /**
   * Ngày sinh (timestamp millis)
   */
  @ApiProperty({
    description: 'Ngày sinh (timestamp millis)',
    example: 946684800000,
  })
  @IsNumber()
  dateOfBirth: number;

  /**
   * Vị trí
   */
  @ApiProperty({
    description: 'Vị trí',
    example: 'Trợ lý AI',
  })
  @IsString()
  position: string;

  /**
   * Học vấn
   */
  @ApiProperty({
    description: 'Học vấn',
    example: 'Đại học',
  })
  @IsString()
  education: string;

  /**
   * Kỹ năng
   */
  @ApiProperty({
    description: 'Kỹ năng',
    example: ['Trả lời câu hỏi', 'Tìm kiếm thông tin'],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  skills: string[];

  /**
   * Tính cách
   */
  @ApiProperty({
    description: 'Tính cách',
    example: ['Thân thiện', 'Kiên nhẫn'],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  personality: string[];

  /**
   * Ngôn ngữ
   */
  @ApiProperty({
    description: 'Ngôn ngữ',
    example: ['Tiếng Việt', 'Tiếng Anh'],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  languages: string[];

  /**
   * Quốc gia
   */
  @ApiProperty({
    description: 'Quốc gia',
    example: 'Việt Nam',
  })
  @IsString()
  nations: string;
}

export class CreateConvertDto {
  @ApiProperty({
    description: 'Tên của field trong schema JSON',
    example: 'email',
  })
  @IsString()
  // @Matches(/^[a-zA-Z_][a-zA-Z0-9_]*$/, {
  //   message: 'Tên field chỉ chứa chữ, số và _ và không bắt đầu bằng số',
  // })
  name: string;

  @ApiProperty({
    description: 'Kiểu dữ liệu của field',
    example: 'string',
  })
  @IsString()
  @IsIn(['string', 'number', 'boolean', 'array', 'object'], {
    message: 'Kiểu dữ liệu không hợp lệ',
  })
  type: 'string' | 'number' | 'boolean' | 'array';

  @ApiProperty({
    description: 'Mô tả (nội dung) của field',
    example: 'Địa chỉ email người dùng',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'Trường này có bắt buộc không?',
    example: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  required?: boolean;

  @ApiProperty({
    description: 'Trường này có được kích hoạt trong schema không?',
    example: true,
    default: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  active?: boolean = true;
}

/**
 * DTO cho việc tạo agent mới
 */
export class CreateAgentDto {
  /**
   * Tên agent
   */
  @ApiProperty({
    description: 'Tên agent',
    example: 'My Assistant',
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  name: string;

  /**
   * ID loại agent
   */
  @ApiProperty({
    description: 'ID loại agent',
    example: 1,
  })
  @IsNumber()
  typeId: number;

  /**
   * MIME type của avatar
   */
  @ApiPropertyOptional({
    description: 'MIME type của avatar',
    example: 'image/jpeg',
  })
  @IsString()
  @IsOptional()
  avatarMimeType?: string;

  /**
   * Cấu hình model
   */
  @ApiProperty({
    description: 'Cấu hình model',
    type: ModelConfigDto,
  })
  @ValidateNested()
  @Type(() => ModelConfigDto)
  modelConfig: ModelConfigDto;

  /**
   * Hướng dẫn (instruction)
   */
  @ApiProperty({
    description: 'Hướng dẫn (instruction)',
    example: 'Bạn là trợ lý cá nhân, hãy giúp người dùng giải đáp các thắc mắc',
  })
  @IsString()
  instruction: string;

  /**
   * Thông tin profile
   */
  @ApiProperty({
    description: 'Thông tin profile',
    type: ProfileDto,
  })
  @ValidateNested()
  @Type(() => ProfileDto)
  profile: ProfileDto;

  /**
   * ID của vector store
   */
  @ApiProperty({
    description: 'ID của vector store',
    example: 'vector-store-1',
  })
  @IsString()
  @IsOptional()
  vectorStoreId?: string;

  /**
   * ID của strategy
   */
  @ApiPropertyOptional({
    description: 'ID của strategy',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsOptional()
  strategyId?: string;

  /**
   * Cấu hình chuyển đổi
   */
  @ApiProperty({
    description: 'Cấu hình chuyển đổi',
    type: [CreateConvertDto],
  })
  @ValidateNested({ each: true })
  @Type(() => CreateConvertDto)
  convertConfig?: CreateConvertDto[];

  /**
   * Danh sách ID của media
   */
  @ApiPropertyOptional({
    description: 'Danh sách ID của media',
    example: ['media-1', 'media-2'],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  mediaIds?: string[];

  /**
   * Danh sách ID của sản phẩm
   */
  @ApiPropertyOptional({
    description: 'Danh sách ID của sản phẩm',
    example: ['1', '2'],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  productIds?: string[];

  /**
   * Danh sách ID của URL
   */
  @ApiPropertyOptional({
    description: 'Danh sách ID của URL',
    example: ['url-1', 'url-2'],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  urlIds?: string[];

  /**
   * Danh sách ID của user custom tools
   */
  @ApiPropertyOptional({
    description: 'Danh sách ID của user custom tools',
    example: ['tool-uuid-1', 'tool-uuid-2'],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  customToolIds?: string[];

  /**
   * ID của base model (system model)
   */
  @ApiPropertyOptional({
    description: 'ID của base model (system model)',
    example: 'base-model-uuid',
  })
  @IsString()
  @IsOptional()
  model_base_id?: string;

  /**
   * ID của finetuning model
   */
  @ApiPropertyOptional({
    description: 'ID của finetuning model',
    example: 'finetuning-model-uuid',
  })
  @IsString()
  @IsOptional()
  model_finetuning_id?: string;

  /**
   * ID của model cá nhân từ provider
   */
  @ApiPropertyOptional({
    description: 'ID của model cá nhân từ provider (phải đi kèm với provider_id)',
    example: 'gpt-4o',
  })
  @IsString()
  @IsOptional()
  model_id?: string;

  /**
   * ID của provider cá nhân
   */
  @ApiPropertyOptional({
    description: 'ID của provider cá nhân (phải đi kèm với model_id)',
    example: 'provider-uuid',
  })
  @IsString()
  @IsOptional()
  @IsValidModelConfig()
  @IsValidUserPersonalModel()
  provider_id?: string;
}
