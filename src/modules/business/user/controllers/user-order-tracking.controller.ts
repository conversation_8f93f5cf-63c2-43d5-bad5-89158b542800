import { <PERSON>, Get, Post, Param, Body, UseGuards, Lo<PERSON>, <PERSON><PERSON>, HttpStatus, Req, Query } from '@nestjs/common';
import { Response, Request } from 'express';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiParam, ApiBody, ApiQuery } from '@nestjs/swagger';
import { JwtUserGuard } from '@modules/auth/guards';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { JwtPayload } from '@modules/auth/guards/jwt.util';
import { UserOrderService } from '../services/user-order.service';
import { AppException } from '@common/exceptions/app.exception';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { CalculateShippingFeeRequestDto, CalculateShippingFeeResponseDto } from '../dto/calculate-shipping-fee.dto';
import { TrackingApiResponseDto } from '../dto/tracking-response.dto';
import { PrintOrderResponseDto } from '../dto/print-order-response.dto';
import { PrintOrderQueryDto } from '../dto/print-order-query.dto';
import { ApiErrorResponse } from '@common/error/api-error-response.decorator';

/**
 * Controller xử lý tracking và webhook cho đơn hàng vận chuyển
 */
@ApiTags('User Orders - Tracking & Shipping')
@Controller('user/orders')
@UseGuards(JwtUserGuard)
@ApiBearerAuth("JWT-auth")
export class UserOrderTrackingController {
  private readonly logger = new Logger(UserOrderTrackingController.name);

  constructor(
    private readonly userOrderService: UserOrderService,
  ) {}

  /**
   * Tracking trạng thái đơn hàng
   */
  @Get(':id/tracking')
  @ApiOperation({
    summary: 'Tracking trạng thái đơn hàng',
    description: 'Lấy thông tin tracking từ đơn vị vận chuyển và cập nhật trạng thái đơn hàng'
  })
  @ApiParam({
    name: 'id',
    description: 'ID của đơn hàng',
    type: 'number',
    example: 1
  })
  @ApiResponse({
    status: 200,
    description: 'Thông tin tracking đơn hàng',
    type: TrackingApiResponseDto
  })
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.ORDER_NOT_FOUND,
    BUSINESS_ERROR_CODES.ORDER_FIND_FAILED,
    BUSINESS_ERROR_CODES.ORDER_ACCESS_DENIED
  )
  async trackOrder(
    @Param('id') orderId: number,
    @CurrentUser() user: JwtPayload
  ) {
    try {
      this.logger.log(`User ${user.id} tracking đơn hàng ${orderId}`);

      const trackingInfo = await this.userOrderService.trackOrder(orderId, user.id);

      return {
        success: true,
        message: 'Lấy thông tin tracking thành công',
        data: trackingInfo
      };
    } catch (error) {
      this.logger.error(`Lỗi khi tracking đơn hàng ${orderId}:`, error);
      throw error;
    }
  }

  /**
   * In đơn hàng
   */
  @Get(':id/print')
  @ApiOperation({
    summary: 'In đơn hàng',
    description: 'Tự động xác định đơn vị vận chuyển từ đơn hàng và in theo API của carrier đó. GHN trả về token, GHTK trả về file PDF trực tiếp.'
  })
  @ApiParam({
    name: 'id',
    description: 'ID của đơn hàng',
    type: 'number',
    example: 20
  })
  @ApiQuery({
    name: 'paperSize',
    description: 'Khổ giấy in (chỉ áp dụng cho GHTK)',
    enum: ['A5', 'A6'],
    required: false,
    example: 'A6'
  })
  @ApiQuery({
    name: 'orientation',
    description: 'Hướng in (chỉ áp dụng cho GHTK)',
    enum: ['portrait', 'landscape'],
    required: false,
    example: 'portrait'
  })
  @ApiQuery({
    name: 'ghnFormat',
    description: 'Format in cho GHN (A5, 80x80, 52x70)',
    enum: ['A5', '80x80', '52x70'],
    required: false,
    example: 'A5'
  })
  @ApiResponse({
    status: 200,
    description: 'GHN: Trả về JSON với token. GHTK: Trả về file PDF trực tiếp.',
    type: PrintOrderResponseDto,
    examples: {
      'GHN Print Token': {
        summary: 'GHN - Trả về token để in đơn hàng',
        value: {
          success: true,
          message: 'Tạo token in đơn hàng thành công',
          data: {
            orderId: '20',
            carrier: 'GHN',
            trackingNumber: 'LBK6X3',
            printType: 'token',
            printData: {
              token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
              printUrl: 'https://online-gateway.ghn.vn/a5/public-api/printA5?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
              instructions: 'Sử dụng URL này để in đơn hàng khổ A5. Token có hiệu lực trong 24 giờ.'
            },
            createdAt: 1749016943203
          }
        }
      },
      'GHTK PDF File': {
        summary: 'GHTK - Trả về file PDF trực tiếp',
        value: 'Binary PDF file content'
      }
    }
  })
  @ApiResponse({
    status: 400,
    description: 'Order has no shipping information for printing',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 30029 },
        message: { type: 'string', example: 'Đơn hàng không có thông tin vận chuyển để in' },
        detail: { type: 'object', example: {} }
      }
    }
  })
  @ApiResponse({
    status: 403,
    description: 'Access denied to this order',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 30027 },
        message: { type: 'string', example: 'Bạn không có quyền truy cập đơn hàng này' },
        detail: { type: 'object', example: {} }
      }
    }
  })
  @ApiResponse({
    status: 404,
    description: 'Order not found',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 30021 },
        message: { type: 'string', example: 'Đơn hàng không tồn tại' },
        detail: { type: 'object', example: {} }
      }
    }
  })
  @ApiResponse({
    status: 500,
    description: 'Failed to print order',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 30028 },
        message: { type: 'string', example: 'Lỗi khi in đơn hàng: API timeout' },
        detail: { type: 'object', example: {} }
      }
    }
  })
  async printOrder(
    @Param('id') orderId: number,
    @CurrentUser() user: JwtPayload,
    @Req() req: Request,
    @Query() query: PrintOrderQueryDto,
    @Res() res: Response
  ) {
    try {
      this.logger.log(`User ${user.id} in đơn hàng ${orderId} với options:`, query);

      const printInfo = await this.userOrderService.printOrder(orderId, user.id, query);

      // Nếu là GHTK, trả về file PDF trực tiếp
      if (printInfo.printType === 'pdf') {
        res.set({
          'Content-Type': 'application/pdf',
          'Content-Disposition': `attachment; filename="${printInfo.printData.fileName}"`,
          'Content-Length': printInfo.pdfBuffer.length.toString(),
        });
        return res.status(HttpStatus.OK).send(printInfo.pdfBuffer);
      }

      // Nếu là GHN, trả về JSON với token
      return res.status(HttpStatus.OK).json({
        success: true,
        message: 'Tạo token in đơn hàng thành công',
        data: printInfo
      });
    } catch (error) {
      this.logger.error(`Lỗi khi in đơn hàng ${orderId}:`, error);

      // Trả về JSON error response
      return res.status(error.status || HttpStatus.INTERNAL_SERVER_ERROR).json({
        code: error.code || 30028,
        message: error.message || 'Lỗi khi in đơn hàng',
        detail: {}
      });
    }
  }



  /**
   * Tính phí vận chuyển trước khi đặt hàng
   */
  @Post('calculate-shipping-fee')
  @ApiOperation({
    summary: 'Tính phí vận chuyển',
    description: 'Tính phí vận chuyển cho đơn hàng trước khi đặt hàng'
  })
  @ApiBody({
    description: 'Thông tin để tính phí vận chuyển',
    type: CalculateShippingFeeRequestDto,
    examples: {
      'Sử dụng địa chỉ customer': {
        summary: 'Sử dụng địa chỉ của khách hàng',
        description: 'Không truyền deliveryAddress, hệ thống sẽ lấy địa chỉ từ customer',
        value: {
          shopId: 1,
          customerId: 18,
          products: [
            { productId: 60, quantity: 2 },
            { productId: 61, quantity: 1 }
          ],
          preferredCarrier: 'GHN'
        }
      },
      'Sử dụng địa chỉ có sẵn': {
        summary: 'Chọn địa chỉ đã lưu trong hệ thống',
        description: 'Sử dụng địa chỉ đã được lưu trước đó',
        value: {
          shopId: 1,
          products: [
            { productId: 60, quantity: 2 },
            { productId: 61, quantity: 1 }
          ],
          deliveryAddress: {
            addressId: 1
          },
          preferredCarrier: 'GHN'
        }
      },
      'Tạo địa chỉ mới': {
        summary: 'Tạo địa chỉ mới để tính phí',
        description: 'Tạo địa chỉ mới và tính phí vận chuyển',
        value: {
          shopId: 1,
          products: [
            { productId: 60, quantity: 2 },
            { productId: 61, quantity: 1 }
          ],
          deliveryAddress: {
            newAddress: {
              recipientName: "Nguyễn Văn A",
              recipientPhone: "0912345678",
              address: "123 Đường ABC, Phường 1",
              province: "TP. Hồ Chí Minh",
              district: "Quận 1",
              ward: "Phường Bến Nghé",
              postalCode: "70000",
              isDefault: false,
              addressType: "home",
              note: "Gần chợ Bến Thành"
            }
          },
          preferredCarrier: 'GHN'
        }
      }
    }
  })
  @ApiResponse({
    status: 200,
    description: 'Phí vận chuyển đã tính',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: 'Tính phí vận chuyển thành công' },
        data: {
          type: 'object',
          properties: {
            carrier: { type: 'string', example: 'GHN' },
            fee: { type: 'number', example: 30000 },
            serviceType: { type: 'string', example: 'standard' },
            estimatedDeliveryTime: { type: 'string', example: '2-3 ngày' }
          }
        }
      }
    }
  })
  async calculateShippingFee(
    @Body() calculateFeeDto: CalculateShippingFeeRequestDto,
    @CurrentUser() user: JwtPayload
  ) {
    try {
      this.logger.log(`User ${user.id} tính phí vận chuyển`);

      // Debug log để kiểm tra input
      this.logger.log('Calculate shipping fee input:', {
        userId: user.id,
        products: calculateFeeDto.products,
        deliveryAddress: calculateFeeDto.deliveryAddress,
        preferredCarrier: calculateFeeDto.preferredCarrier
      });

      // Sử dụng method public để tính phí vận chuyển với dữ liệu thực từ database
      const shippingResult = await this.userOrderService.calculateShippingFeeForProducts(
        user.id,
        calculateFeeDto.shopId,
        calculateFeeDto.products,
        calculateFeeDto.deliveryAddress,
        calculateFeeDto.preferredCarrier,
        calculateFeeDto.customerId
      );

      // Debug log để kiểm tra output
      this.logger.log('Calculate shipping fee result:', shippingResult);

      return {
        success: true,
        message: 'Tính phí vận chuyển thành công',
        data: shippingResult
      };
    } catch (error) {
      this.logger.error(`Lỗi khi tính phí vận chuyển:`, error);
      throw error;
    }
  }


}

/**
 * Controller xử lý webhook từ các đơn vị vận chuyển
 */
@ApiTags('Shipping Webhooks')
@Controller('webhooks/shipping')
export class ShippingWebhookController {
  private readonly logger = new Logger(ShippingWebhookController.name);

  constructor(
    private readonly userOrderService: UserOrderService,
  ) {}

  /**
   * Webhook từ GHN
   */
  @Post('ghn')
  @ApiOperation({
    summary: 'Webhook từ GHN',
    description: 'Nhận cập nhật trạng thái đơn hàng từ GHN'
  })
  @ApiBody({
    description: 'Dữ liệu webhook từ GHN',
    schema: {
      type: 'object',
      properties: {
        Type: { type: 'string', example: 'switch_status' },
        OrderCode: { type: 'string', example: 'GHN123456789' },
        Status: { type: 'string', example: 'delivered' },
        Description: { type: 'string', example: 'Đã giao hàng thành công' },
        Time: { type: 'string', example: '2024-01-01 10:00:00' }
      }
    }
  })
  @ApiResponse({
    status: 200,
    description: 'Webhook đã được xử lý thành công'
  })
  async handleGHNWebhook(@Body() webhookData: any) {
    try {
      this.logger.log('Nhận webhook từ GHN', webhookData);
      
      await this.userOrderService.handleShippingWebhook(webhookData, 'GHN');
      
      return {
        success: true,
        message: 'Webhook GHN đã được xử lý thành công'
      };
    } catch (error) {
      this.logger.error('Lỗi khi xử lý webhook GHN:', error);
      return {
        success: false,
        message: 'Lỗi khi xử lý webhook GHN'
      };
    }
  }

  /**
   * Webhook từ GHTK
   */
  @Post('ghtk')
  @ApiOperation({
    summary: 'Webhook từ GHTK',
    description: 'Nhận cập nhật trạng thái đơn hàng từ GHTK'
  })
  @ApiBody({
    description: 'Dữ liệu webhook từ GHTK',
    schema: {
      type: 'object',
      properties: {
        partner_id: { type: 'string', example: 'ORDER_123_1641708800000' },
        label_id: { type: 'string', example: 'GHTK123456789' },
        status_id: { type: 'number', example: 5 },
        action_time: { type: 'string', example: '2024-01-01 10:00:00' },
        reason_code: { type: 'string', example: '1' },
        reason: { type: 'string', example: 'Đã giao hàng thành công' }
      }
    }
  })
  @ApiResponse({
    status: 200,
    description: 'Webhook đã được xử lý thành công'
  })
  async handleGHTKWebhook(@Body() webhookData: any) {
    try {
      this.logger.log('Nhận webhook từ GHTK', webhookData);
      
      await this.userOrderService.handleShippingWebhook(webhookData, 'GHTK');
      
      return {
        success: true,
        message: 'Webhook GHTK đã được xử lý thành công'
      };
    } catch (error) {
      this.logger.error('Lỗi khi xử lý webhook GHTK:', error);
      return {
        success: false,
        message: 'Lỗi khi xử lý webhook GHTK'
      };
    }
  }
}
