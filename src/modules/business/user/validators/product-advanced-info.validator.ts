import {
  ValidatorConstraint,
  ValidatorConstraintInterface,
  ValidationArguments,
  registerDecorator,
  ValidationOptions,
} from 'class-validator';
import { ProductTypeEnum } from '@modules/business/enums';

/**
 * Validator để kiểm tra tính hợp lệ của advancedInfo theo productType
 */
@ValidatorConstraint({ name: 'isValidAdvancedInfo', async: false })
export class IsValidAdvancedInfoConstraint implements ValidatorConstraintInterface {
  validate(advancedInfo: any, args: ValidationArguments) {
    const object = args.object as any;
    const productType = object.productType;

    // Nếu không có advancedInfo, luôn hợp lệ
    if (!advancedInfo) {
      return true;
    }

    // Nếu productType là PHYSICAL, không được có advancedInfo
    if (productType === ProductTypeEnum.PHYSICAL) {
      return false;
    }

    // Kiểm tra theo từng loại sản phẩm
    switch (productType) {
      case ProductTypeEnum.DIGITAL:
        return this.validateDigitalProduct(advancedInfo);

      case ProductTypeEnum.EVENT:
        return this.validateEventProduct(advancedInfo);

      case ProductTypeEnum.SERVICE:
        return this.validateServiceProduct(advancedInfo);

      case ProductTypeEnum.COMBO:
        return this.validateComboProduct(advancedInfo);

      default:
        return false;
    }
  }

  defaultMessage(args: ValidationArguments) {
    const object = args.object as any;
    const productType = object.productType;

    if (productType === ProductTypeEnum.PHYSICAL) {
      return 'Sản phẩm vật lý không được có thông tin nâng cao';
    }

    return `Thông tin nâng cao không hợp lệ cho loại sản phẩm ${productType}`;
  }

  /**
   * Validate thông tin nâng cao cho sản phẩm số
   */
  private validateDigitalProduct(advancedInfo: any): boolean {
    if (!advancedInfo.digitalFulfillmentFlow || !advancedInfo.digitalOutput) {
      return false;
    }

    // Kiểm tra digitalFulfillmentFlow
    const flow = advancedInfo.digitalFulfillmentFlow;
    if (!flow.deliveryMethod || !flow.deliveryTiming || !flow.accessStatus) {
      return false;
    }

    // Kiểm tra digitalOutput
    const output = advancedInfo.digitalOutput;
    if (!output.outputType) {
      return false;
    }

    return true;
  }

  /**
   * Validate thông tin nâng cao cho sự kiện
   */
  private validateEventProduct(advancedInfo: any): boolean {
    if (!advancedInfo.eventFormat || !advancedInfo.startDate || !advancedInfo.endDate || !advancedInfo.timezone) {
      return false;
    }

    // Kiểm tra eventFormat và các trường liên quan
    const format = advancedInfo.eventFormat;
    if (format === 'ONLINE' && !advancedInfo.eventLink) {
      return false;
    }
    if (format === 'OFFLINE' && !advancedInfo.eventLocation) {
      return false;
    }
    if (format === 'HYBRID' && (!advancedInfo.eventLink || !advancedInfo.eventLocation)) {
      return false;
    }

    // Kiểm tra ticketTypes
    if (!advancedInfo.ticketTypes || !Array.isArray(advancedInfo.ticketTypes) || advancedInfo.ticketTypes.length === 0) {
      return false;
    }

    return true;
  }

  /**
   * Validate thông tin nâng cao cho dịch vụ
   */
  private validateServiceProduct(advancedInfo: any): boolean {
    // Kiểm tra servicePackages
    if (!advancedInfo.servicePackages || !Array.isArray(advancedInfo.servicePackages) || advancedInfo.servicePackages.length === 0) {
      return false;
    }

    return true;
  }

  /**
   * Validate thông tin nâng cao cho combo sản phẩm
   */
  private validateComboProduct(advancedInfo: any): boolean {
    // Kiểm tra info array
    if (!advancedInfo.info || !Array.isArray(advancedInfo.info) || advancedInfo.info.length === 0) {
      return false;
    }

    // Kiểm tra từng item trong combo
    for (const item of advancedInfo.info) {
      if (!item.productId || !item.total || typeof item.productId !== 'number' || typeof item.total !== 'number') {
        return false;
      }
      if (item.productId <= 0 || item.total <= 0) {
        return false;
      }
    }

    return true;
  }
}

/**
 * Decorator để validate advancedInfo
 */
export function IsValidAdvancedInfo(validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      constraints: [],
      validator: IsValidAdvancedInfoConstraint,
    });
  };
}
