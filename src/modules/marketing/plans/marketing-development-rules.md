# 📋 Marketing Module Development Rules

> <PERSON>uy tắc phát triển cho module Marketing dựa trên cấu trúc hiện tại và kế hoạch SMS/Email Marketing

## 🏗️ Cấu trúc Module Hiện Tại

### 1. Phân chia Admin/User
- **Admin**: <PERSON><PERSON><PERSON><PERSON> lý marketing từ phía admin (employees)
- **User**: <PERSON><PERSON><PERSON>ng tác marketing từ phía người dùng (users)
- **Common**: <PERSON><PERSON><PERSON> thành phần dùng chung

### 2. Entities Hiện Có

#### 2.1. Admin Entities
- `AdminTemplateEmail` - Mẫu email admin
- `AdminTemplateSms` - Mẫu SMS admin  
- `AdminCampaign` - Chiến dịch marketing admin
- `AdminCampaignHistory` - Lịch sử chiến dịch admin
- `AdminAudience` - Đ<PERSON>i tượng khách hàng admin
- `AdminAudienceCustomField` - Trường tùy chỉnh audience admin
- `AdminAudienceCustomFieldDefinition` - <PERSON><PERSON><PERSON> nghĩa trường tùy chỉnh admin
- `AdminTag` - <PERSON>hãn admin
- `AdminSegment` - Phân đoạn khách hàng admin

#### 2.2. User Entities
- `UserTemplateEmail` - Mẫu email user
- `UserCampaign` - Chiến dịch marketing user
- `UserCampaignHistory` - Lịch sử chiến dịch user
- `UserAudience` - Đối tượng khách hàng user
- `UserAudienceCustomField` - Trường tùy chỉnh audience user
- `UserAudienceCustomFieldDefinition` - Định nghĩa trường tùy chỉnh user
- `UserTag` - Nhãn user
- `UserSegment` - Phân đoạn khách hàng user

## 🎯 Quy Tắc Entity

### 1. Naming Convention
- **Admin entities**: `Admin[EntityName]` (VD: `AdminCampaign`)
- **User entities**: `User[EntityName]` (VD: `UserCampaign`)
- **Table names**: `admin_[table_name]` và `user_[table_name]`

### 2. Common Fields
```typescript
// Tất cả entities phải có:
@Column({ name: 'created_at', type: 'bigint', nullable: true })
createdAt: number;

@Column({ name: 'updated_at', type: 'bigint', nullable: true })
updatedAt: number;

// Admin entities thêm:
@Column({ name: 'created_by', nullable: true })
createdBy: number; // Employee ID

@Column({ name: 'updated_by', nullable: true })
updatedBy: number; // Employee ID

// User entities thêm:
@Column({ name: 'user_id', nullable: true })
userId: number; // User ID
```

### 3. Relationship Rules
- **KHÔNG sử dụng TypeORM relationships** (@ManyToOne, @OneToMany, etc.)
- **CHỈ lưu ID** và comment rõ ràng
- **Sử dụng Repository pattern** để join dữ liệu khi cần

### 4. Campaign Entity Structure
```typescript
// Cấu trúc hiện tại của Campaign
@Entity('admin_campaigns' | 'user_campaigns')
export class AdminCampaign | UserCampaign {
  @PrimaryGeneratedColumn({ name: 'id', type: 'bigint' })
  id: number;

  @Column({ name: 'title', length: 255, nullable: true })
  title: string;

  @Column({ name: 'description', type: 'text', nullable: true })
  description: string;

  @Column({ name: 'platform', length: 255, nullable: true })
  platform: string; // 'email', 'sms', 'zalo'

  @Column({ name: 'content', type: 'text', nullable: true })
  content: string;

  @Column({ name: 'server', type: 'jsonb', nullable: true })
  server: any; // Thông tin máy chủ gửi

  @Column({ name: 'scheduled_at', type: 'bigint', nullable: true })
  scheduledAt: number;

  @Column({ name: 'subject', length: 255, nullable: true })
  subject: string; // Chỉ cho email

  @Column({ name: 'status', length: 20, nullable: true })
  status: string;

  // User campaigns có thêm:
  @Column({ name: 'segment_id', type: 'bigint', nullable: true })
  segmentId: number | null;

  @Column({ name: 'audience_ids', type: 'jsonb', nullable: true })
  audienceIds: number[] | null;
}
```

## 🔧 Quy Tắc Service

### 1. Service Structure
```typescript
@Injectable()
export class Admin[Entity]Service {
  constructor(
    private readonly admin[Entity]Repository: Admin[Entity]Repository,
    // Inject các repository khác nếu cần
  ) {}

  @Transactional()
  async create(createDto: Create[Entity]Dto, employeeId?: number): Promise<[Entity]ResponseDto> {
    // Implementation
  }

  async findAll(query: [Entity]QueryDto): Promise<PaginatedResponseDto<[Entity]ResponseDto>> {
    // Implementation với phân trang
  }

  async findOne(id: number): Promise<[Entity]ResponseDto> {
    // Implementation
  }

  @Transactional()
  async update(id: number, updateDto: Update[Entity]Dto, employeeId?: number): Promise<[Entity]ResponseDto> {
    // Implementation
  }

  @Transactional()
  async remove(id: number): Promise<boolean> {
    // Implementation
  }
}
```

### 2. Error Handling
- Sử dụng `AppException` với `ErrorCode` cụ thể
- Định nghĩa error codes trong module-specific error files
- Ví dụ: `ErrorCode.CAMPAIGN_NOT_FOUND`, `ErrorCode.TEMPLATE_NOT_FOUND`

### 3. Validation
- Sử dụng class-validator trong DTOs
- Tạo ValidationHelper cho business logic validation
- Validate dữ liệu trước khi lưu database

## 🎮 Quy Tắc Controller

### 1. Controller Structure
```typescript
@ApiTags(SWAGGER_API_TAGS.ADMIN_[ENTITY])
@Controller('admin/marketing/[entities]')
@UseGuards(JwtEmployeeGuard)
@ApiBearerAuth()
export class Admin[Entity]Controller {
  constructor(private readonly admin[Entity]Service: Admin[Entity]Service) {}

  @Post()
  @ApiOperation({ summary: 'Tạo [entity] mới' })
  async create(@Body() createDto: Create[Entity]Dto, @CurrentEmployee() employee: JwtPayload) {
    // Implementation
  }

  @Get()
  @ApiOperation({ summary: 'Lấy danh sách [entity]' })
  async findAll(@Query() query: [Entity]QueryDto) {
    // Implementation
  }

  // Các endpoints khác...
}
```

### 2. Response Format
- Sử dụng `wrapResponse()` helper
- Trả về `AppApiResponse<T>` format
- Phân trang sử dụng `PaginatedResponseDto<T>`

## 📊 Quy Tắc Repository

### 1. Repository Pattern
```typescript
@Injectable()
export class Admin[Entity]Repository {
  constructor(
    @InjectRepository(Admin[Entity])
    private readonly repository: Repository<Admin[Entity]>,
  ) {}

  async find(options?: FindManyOptions<Admin[Entity]>): Promise<Admin[Entity][]> {
    return this.repository.find(options);
  }

  async findOne(options?: FindOneOptions<Admin[Entity]>): Promise<Admin[Entity] | null> {
    return this.repository.findOne(options);
  }

  async save(entity: Admin[Entity]): Promise<Admin[Entity]> {
    return this.repository.save(entity);
  }

  async remove(entity: Admin[Entity]): Promise<Admin[Entity]> {
    return this.repository.remove(entity);
  }

  async count(options?: FindManyOptions<Admin[Entity]>): Promise<number> {
    return this.repository.count(options);
  }
}
```

### 2. Query Optimization
- Sử dụng QueryBuilder cho complex queries
- Implement pagination properly
- Add indexes cho các trường thường query

## 🚀 Quy Tắc Phát Triển SMS/Email Marketing

### 1. Entities Mới Cần Tạo

#### 1.1. Email Campaign Entities
```typescript
// KHÔNG tạo AdminEmailCampaign riêng
// SỬ DỤNG AdminCampaign hiện có với platform = 'email'

// Tạo thêm entities cho tracking:
@Entity('admin_email_campaign_stats')
export class AdminEmailCampaignStats {
  @PrimaryGeneratedColumn({ name: 'id' })
  id: number;

  @Column({ name: 'campaign_id', nullable: false })
  campaignId: number;

  @Column({ name: 'sent_count', type: 'int', default: 0 })
  sentCount: number;

  @Column({ name: 'delivered_count', type: 'int', default: 0 })
  deliveredCount: number;

  @Column({ name: 'open_count', type: 'int', default: 0 })
  openCount: number;

  @Column({ name: 'click_count', type: 'int', default: 0 })
  clickCount: number;

  @Column({ name: 'bounce_count', type: 'int', default: 0 })
  bounceCount: number;

  @Column({ name: 'unsubscribe_count', type: 'int', default: 0 })
  unsubscribeCount: number;

  @Column({ name: 'created_at', type: 'bigint', nullable: false })
  createdAt: number;

  @Column({ name: 'updated_at', type: 'bigint', nullable: true })
  updatedAt: number;
}
```

#### 1.2. SMS Campaign Entities
```typescript
// SỬ DỤNG AdminCampaign hiện có với platform = 'sms'

// Tạo thêm entities cho SMS replies:
@Entity('admin_sms_replies')
export class AdminSmsReply {
  @PrimaryGeneratedColumn({ name: 'id' })
  id: number;

  @Column({ name: 'campaign_id', nullable: true })
  campaignId: number;

  @Column({ name: 'audience_id', nullable: false })
  audienceId: number;

  @Column({ name: 'phone', length: 20, nullable: false })
  phone: string;

  @Column({ name: 'message', type: 'text', nullable: false })
  message: string;

  @Column({ name: 'received_at', type: 'bigint', nullable: false })
  receivedAt: number;

  @Column({ name: 'created_at', type: 'bigint', nullable: false })
  createdAt: number;
}
```

### 2. Campaign Platform Values
- `'email'` - Email campaigns
- `'sms'` - SMS campaigns  
- `'zalo'` - Zalo campaigns (đã có)
- `'facebook'` - Facebook campaigns (tương lai)

### 3. Campaign Status Values
- `'draft'` - Nháp
- `'scheduled'` - Đã lên lịch
- `'sending'` - Đang gửi
- `'sent'` - Đã gửi
- `'paused'` - Tạm dừng
- `'cancelled'` - Đã hủy
- `'completed'` - Hoàn thành

### 4. Server Configuration
```typescript
// Cấu trúc server field trong Campaign
interface EmailServerConfig {
  type: 'smtp' | 'sendgrid' | 'ses';
  host?: string;
  port?: number;
  username?: string;
  password?: string;
  apiKey?: string;
  fromEmail: string;
  fromName: string;
  replyTo?: string;
}

interface SmsServerConfig {
  type: 'twilio' | 'nexmo' | 'infobip';
  apiKey: string;
  apiSecret?: string;
  senderId: string;
  accountSid?: string; // Twilio
}
```

## ✅ Checklist Phát Triển

### Trước khi tạo Entity mới:
- [ ] Kiểm tra entity hiện có có thể mở rộng không
- [ ] Tuân thủ naming convention
- [ ] Thêm đầy đủ comments cho các fields
- [ ] Sử dụng đúng data types

### Trước khi tạo Service:
- [ ] Implement đầy đủ CRUD operations
- [ ] Sử dụng @Transactional cho operations thay đổi data
- [ ] Handle errors với AppException
- [ ] Validate input data

### Trước khi tạo Controller:
- [ ] Thêm đầy đủ Swagger documentation
- [ ] Sử dụng đúng Guards và Decorators
- [ ] Implement pagination cho list endpoints
- [ ] Sử dụng wrapResponse helper

### Trước khi deploy:
- [ ] Viết unit tests
- [ ] Kiểm tra performance queries
- [ ] Validate API responses
- [ ] Update documentation

## 🚫 Điều Cấm

1. **KHÔNG tạo entities mới** nếu có thể mở rộng entities hiện có
2. **KHÔNG sử dụng TypeORM relationships** (@ManyToOne, @OneToMany)
3. **KHÔNG hardcode values** - sử dụng enums và constants
4. **KHÔNG bỏ qua validation** - validate tất cả input data
5. **KHÔNG quên phân quyền** - luôn check user permissions
6. **KHÔNG duplicate code** - sử dụng helpers và utilities

## 🔄 Quy Tắc Migration và Database

### 1. Database Schema Changes
```sql
-- Ví dụ migration cho email campaign stats
CREATE TABLE admin_email_campaign_stats (
    id BIGSERIAL PRIMARY KEY,
    campaign_id BIGINT NOT NULL,
    sent_count INTEGER DEFAULT 0,
    delivered_count INTEGER DEFAULT 0,
    open_count INTEGER DEFAULT 0,
    click_count INTEGER DEFAULT 0,
    bounce_count INTEGER DEFAULT 0,
    unsubscribe_count INTEGER DEFAULT 0,
    created_at BIGINT NOT NULL,
    updated_at BIGINT
);

-- Index cho performance
CREATE INDEX idx_email_campaign_stats_campaign_id ON admin_email_campaign_stats(campaign_id);
CREATE INDEX idx_email_campaign_stats_created_at ON admin_email_campaign_stats(created_at);
```

### 2. Migration Rules
- **Luôn backup** database trước khi chạy migration
- **Test migration** trên staging environment trước
- **Tạo rollback script** cho mọi migration
- **Document changes** trong migration comments

## 🎨 Quy Tắc DTO

### 1. DTO Naming Convention
```typescript
// Create DTOs
export class CreateEmailCampaignDto {
  @IsString()
  @IsNotEmpty()
  name: string;

  @IsString()
  @IsNotEmpty()
  subject: string;

  @IsOptional()
  @IsNumber()
  templateId?: number;

  @IsOptional()
  @IsNumber()
  segmentId?: number;

  @IsOptional()
  @IsArray()
  @IsNumber({}, { each: true })
  audienceIds?: number[];
}

// Update DTOs
export class UpdateEmailCampaignDto extends PartialType(CreateEmailCampaignDto) {}

// Response DTOs
export class EmailCampaignResponseDto {
  id: number;
  name: string;
  subject: string;
  platform: string;
  status: string;
  scheduledAt?: number;
  createdAt: number;
  updatedAt?: number;

  // Stats nếu có
  stats?: EmailCampaignStatsDto;
}

// Query DTOs
export class EmailCampaignQueryDto extends PaginationDto {
  @IsOptional()
  @IsString()
  name?: string;

  @IsOptional()
  @IsString()
  status?: string;

  @IsOptional()
  @IsString()
  platform?: string;
}
```

### 2. Validation Rules
- **Luôn validate** tất cả input fields
- **Sử dụng class-validator** decorators
- **Custom validators** cho business logic
- **Transform data** khi cần thiết

## 🔐 Quy Tắc Security

### 1. Authentication & Authorization
```typescript
// Admin endpoints
@UseGuards(JwtEmployeeGuard)
@Controller('admin/marketing/campaigns')
export class AdminCampaignController {
  // Chỉ employees có thể access
}

// User endpoints
@UseGuards(JwtAuthGuard)
@Controller('v1/user/marketing/campaigns')
export class UserCampaignController {
  // Chỉ users có thể access
  // Luôn filter theo userId
}
```

### 2. Data Access Rules
- **Admin**: Có thể xem tất cả data
- **User**: Chỉ xem data của mình (filter theo userId)
- **Validate ownership** trước khi cho phép thao tác
- **Log sensitive operations** cho audit trail

## 📈 Quy Tắc Performance

### 1. Database Optimization
```typescript
// Sử dụng indexes
@Index(['campaignId', 'createdAt'])
@Entity('admin_email_campaign_stats')
export class AdminEmailCampaignStats {
  // Entity definition
}

// Pagination hiệu quả
async findCampaigns(query: CampaignQueryDto): Promise<PaginatedResponseDto<CampaignResponseDto>> {
  const { page = 1, limit = 10 } = query;
  const offset = (page - 1) * limit;

  // Sử dụng QueryBuilder cho complex queries
  const queryBuilder = this.repository.createQueryBuilder('campaign')
    .where('campaign.platform = :platform', { platform: 'email' })
    .orderBy('campaign.createdAt', 'DESC')
    .skip(offset)
    .take(limit);

  const [campaigns, total] = await queryBuilder.getManyAndCount();

  return {
    data: campaigns.map(c => this.mapToDto(c)),
    meta: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit)
    }
  };
}
```

### 2. Caching Strategy
- **Cache template data** (ít thay đổi)
- **Cache segment results** (expensive queries)
- **Invalidate cache** khi data thay đổi
- **Use Redis** cho session và temporary data

## 🧪 Quy Tắc Testing

### 1. Unit Tests
```typescript
describe('AdminCampaignService', () => {
  let service: AdminCampaignService;
  let repository: AdminCampaignRepository;

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      providers: [
        AdminCampaignService,
        {
          provide: AdminCampaignRepository,
          useValue: {
            save: jest.fn(),
            findOne: jest.fn(),
            find: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<AdminCampaignService>(AdminCampaignService);
    repository = module.get<AdminCampaignRepository>(AdminCampaignRepository);
  });

  describe('create', () => {
    it('should create email campaign successfully', async () => {
      // Test implementation
    });

    it('should throw error when template not found', async () => {
      // Test error cases
    });
  });
});
```

### 2. Integration Tests
- Test API endpoints end-to-end
- Test database operations
- Test external service integrations
- Mock external dependencies

## 📧 Quy Tắc Email/SMS Integration

### 1. Email Service Integration
```typescript
interface EmailProvider {
  sendEmail(config: EmailConfig, recipients: string[], content: string): Promise<SendResult>;
  getDeliveryStatus(messageId: string): Promise<DeliveryStatus>;
}

// Implement cho từng provider
export class SendGridProvider implements EmailProvider {
  async sendEmail(config: EmailConfig, recipients: string[], content: string): Promise<SendResult> {
    // SendGrid implementation
  }
}

export class SMTPProvider implements EmailProvider {
  async sendEmail(config: EmailConfig, recipients: string[], content: string): Promise<SendResult> {
    // SMTP implementation
  }
}
```

### 2. SMS Service Integration
```typescript
interface SmsProvider {
  sendSms(config: SmsConfig, recipients: string[], message: string): Promise<SendResult>;
  getDeliveryStatus(messageId: string): Promise<DeliveryStatus>;
}

// Implement cho từng provider
export class TwilioProvider implements SmsProvider {
  async sendSms(config: SmsConfig, recipients: string[], message: string): Promise<SendResult> {
    // Twilio implementation
  }
}
```

### 3. Queue Processing
- Sử dụng **BullMQ** cho background jobs
- **Retry mechanism** cho failed sends
- **Rate limiting** để tránh spam
- **Progress tracking** cho large campaigns

## 📊 Quy Tắc Analytics & Reporting

### 1. Tracking Events
```typescript
// Email tracking events
enum EmailEvent {
  SENT = 'sent',
  DELIVERED = 'delivered',
  OPENED = 'opened',
  CLICKED = 'clicked',
  BOUNCED = 'bounced',
  UNSUBSCRIBED = 'unsubscribed'
}

// SMS tracking events
enum SmsEvent {
  SENT = 'sent',
  DELIVERED = 'delivered',
  FAILED = 'failed',
  REPLIED = 'replied'
}
```

### 2. Metrics Calculation
```typescript
export class CampaignAnalyticsService {
  calculateEmailMetrics(stats: EmailCampaignStats): EmailMetrics {
    const openRate = stats.sentCount > 0 ? (stats.openCount / stats.sentCount) * 100 : 0;
    const clickRate = stats.openCount > 0 ? (stats.clickCount / stats.openCount) * 100 : 0;
    const bounceRate = stats.sentCount > 0 ? (stats.bounceCount / stats.sentCount) * 100 : 0;

    return {
      openRate: Math.round(openRate * 100) / 100,
      clickRate: Math.round(clickRate * 100) / 100,
      bounceRate: Math.round(bounceRate * 100) / 100,
      deliveryRate: Math.round(((stats.sentCount - stats.bounceCount) / stats.sentCount) * 100 * 100) / 100
    };
  }
}
```

## 📝 Ghi Chú Quan Trọng

### Thực Tế vs Kế Hoạch
- **Kế hoạch đề xuất** tạo `AdminEmailCampaign` và `AdminSmsCampaign` riêng
- **Thực tế hiện tại** đã có `AdminCampaign` với field `platform` linh hoạt
- **Quyết định**: Sử dụng cấu trúc hiện có, chỉ tạo thêm tracking entities

### Ưu Tiên Phát Triển
1. **Mở rộng Campaign service** cho email/SMS
2. **Tạo tracking entities** cho analytics
3. **Implement email/SMS providers**
4. **Phát triển queue system** cho bulk sending
5. **Tạo analytics dashboard**

### Lưu Ý Đặc Biệt
- Campaign entity hiện tại **ĐÃ ĐỦ LINH HOẠT** cho email/SMS
- **KHÔNG cần tạo entities mới** cho campaign
- **TẬP TRUNG** vào tracking, analytics và integration
- **MAINTAIN CONSISTENCY** với codebase hiện tại
