# 📋 Tài Liệu Đ<PERSON>y Đủ <PERSON>c T<PERSON>h Năng Marketing Cho User

> Tài liệu chi tiết về tất cả các tính năng marketing dành cho user, bao gồm worker, nhà cung cấp dịch vụ và tích hợp

## 🏗️ Tổng Quan Kiến Trúc Hệ Thống

### 1. Cấu Trúc Module Marketing User
```
src/modules/marketing/user/
├── controllers/              # 19 Controllers
│   ├── user-tag.controller.ts
│   ├── user-audience.controller.ts
│   ├── user-segment.controller.ts
│   ├── user-campaign.controller.ts
│   ├── user-template-email.controller.ts
│   ├── email-campaign.controller.ts
│   ├── marketing-overview.controller.ts
│   ├── user-marketing-statistics.controller.ts
│   ├── user-audience-custom-field-definition.controller.ts
│   └── zalo-*.controller.ts (10 controllers Zalo)
├── services/                 # Business Logic Services
├── entities/                 # 30+ Database Entities
├── dto/                      # Data Transfer Objects
├── repositories/             # Data Access Layer
├── migrations/               # Database Migrations
└── queue-integration-guide.md # Worker Integration Guide
```

### 2. Hệ Thống Queue & Worker
```typescript
// Queue Configuration
BullModule.registerQueue({
  name: QueueName.EMAIL_MARKETING,
})

// Supported Queues
- EMAIL_MARKETING: Xử lý email campaigns
- SMS: Xử lý tin nhắn SMS
- NOTIFICATION: Xử lý thông báo
- DATA_PROCESS: Xử lý dữ liệu
- CRAWL_URL: Xử lý crawl dữ liệu
```

### 3. Nhà Cung Cấp Dịch Vụ Tích Hợp

#### 3.1. Email Service Providers
```typescript
// SMTP Configuration
interface EmailServerConfig {
  host: string;           // smtp.gmail.com, smtp.mailgun.org
  port: number;          // 465, 587
  username: string;      // Email account
  password: string;      // App password/token
  useSsl: boolean;       // SSL/TLS encryption
  useStartTls: boolean;  // STARTTLS
}

// Supported Email Providers
- SMTP (Gmail, Outlook, Yahoo)
- Mailgun
- SendGrid (planned)
- Amazon SES (planned)
```

#### 3.2. SMS Service Providers
```typescript
// SMS Provider Factory
enum SmsProviderType {
  SPEED_SMS = 'SPEED_SMS',    // Việt Nam
  TWILIO = 'TWILIO',          // Quốc tế
  VONAGE = 'VONAGE',          // Quốc tế (ex-Nexmo)
  FPT_SMS = 'FPT_SMS'         // Việt Nam
}

// SMS Configuration
interface SmsConfig {
  provider: SmsProviderType;
  apiKey: string;
  apiSecret?: string;
  accountSid?: string;        // Twilio
  authToken?: string;         // Twilio
  senderId: string;
}
```

#### 3.3. Zalo Integration (Đã Triển Khai Đầy Đủ)
```typescript
// Zalo Services
- ZaloService: Core Zalo API integration
- ZaloOaService: Official Account management
- ZaloZnsService: Zalo Notification Service
- ZaloWebhookService: Webhook handling
- ZaloAgentService: AI Agent integration

// Zalo Features
- Official Account connection
- ZNS template management
- Message sending/receiving
- Follower management
- Automation workflows
- Campaign management
- Analytics & reporting
```

## 🎯 Tính Năng Core Marketing

### 1. Quản Lý Nhãn (Tag Management)

#### 1.1. API Endpoints
```typescript
Base URL: /v1/user/marketing/tags

GET    /                    # Lấy danh sách tag
POST   /                    # Tạo tag mới
GET    /:id                 # Lấy chi tiết tag
PUT    /:id                 # Cập nhật tag
DELETE /:id                 # Xóa tag
```

#### 1.2. Tính Năng Chi Tiết
```typescript
// Tag Entity
interface UserTag {
  id: number;
  userId: number;           // User sở hữu
  name: string;            // Tên tag (unique per user)
  color: string;           // Màu sắc hex (#3498db)
  createdAt: number;       // Unix timestamp
  updatedAt: number;
}

// Business Rules
- Tên tag unique trong phạm vi user
- Màu sắc mặc định: #3498db
- Tự động gán userId từ JWT token
- Soft delete (không xóa vĩnh viễn)
- Audit log cho mọi thay đổi
```

#### 1.3. Validation Rules
```typescript
@IsNotEmpty({ message: 'Tên tag không được để trống' })
@IsString({ message: 'Tên tag phải là chuỗi' })
@MaxLength(255, { message: 'Tên tag không được vượt quá 255 ký tự' })
name: string;

@IsOptional()
@IsHexColor({ message: 'Màu sắc phải là mã hex hợp lệ' })
color?: string;
```

### 2. Quản Lý Đối Tượng (Audience Management)

#### 2.1. API Endpoints
```typescript
Base URL: /v1/user/marketing/audiences

GET    /                    # Lấy danh sách audience (có phân trang)
POST   /                    # Tạo audience mới
GET    /:id                 # Lấy chi tiết audience
PUT    /:id                 # Cập nhật audience
DELETE /:id                 # Xóa audience

// Bulk Operations
POST   /bulk               # Import từ CSV/Excel
PUT    /bulk               # Cập nhật hàng loạt
DELETE /bulk               # Xóa nhiều audience
```

#### 2.2. Audience Entity Structure
```typescript
interface UserAudience {
  id: number;
  userId: number;           // User sở hữu
  email: string;           // Email (required, unique per user)
  phone?: string;          // Số điện thoại (optional)
  createdAt: number;
  updatedAt: number;
}

// Custom Fields Support
interface UserAudienceCustomField {
  id: number;
  audienceId: number;
  fieldName: string;       // Tên trường tùy chỉnh
  fieldValue: any;         // Giá trị (JSONB)
  fieldType: string;       // string, number, date, boolean
}
```

#### 2.3. Advanced Features
```typescript
// Search & Filter
GET /audiences?email=xxx&phone=xxx&tagId=xxx
GET /audiences?customFieldName=xxx&customFieldValue=xxx

// Pagination
interface PaginationQuery {
  page: number;            // Trang hiện tại (default: 1)
  limit: number;           // Số item/trang (default: 10)
  sortBy: string;          // Trường sắp xếp (default: createdAt)
  sortDirection: 'ASC' | 'DESC'; // Hướng sắp xếp
}

// Import/Export
- Hỗ trợ CSV, Excel formats
- Validation dữ liệu trước khi import
- Background processing cho datasets lớn
- Error reporting cho failed imports
- Progress tracking
```

### 3. Quản Lý Phân Đoạn (Segment Management)

#### 3.1. API Endpoints
```typescript
Base URL: /v1/user/marketing/segments

GET    /                    # Lấy danh sách segment
POST   /                    # Tạo segment mới
GET    /:id                 # Lấy chi tiết segment
PUT    /:id                 # Cập nhật segment
DELETE /:id                 # Xóa segment

// Advanced Operations
POST   /:id/preview         # Preview audience trong segment
GET    /:id/audiences       # Lấy audience trong segment
POST   /bulk-delete         # Xóa nhiều segment
```

#### 3.2. Dynamic Segmentation
```typescript
// Segment Criteria Structure
interface SegmentCriteria {
  operator: 'AND' | 'OR';
  conditions: SegmentCondition[];
}

interface SegmentCondition {
  field: string;           // 'email', 'phone', 'customField.name'
  operator: string;        // 'equals', 'contains', 'startsWith', 'gt', 'lt'
  value: any;             // Giá trị so sánh
  type: 'string' | 'number' | 'date' | 'boolean';
}

// Real-time Calculation
- Tính toán audience real-time khi apply criteria
- Cache kết quả trong 5 phút
- Background recalculation cho segments lớn
- Preview trước khi save (limit 100 samples)
```

#### 3.3. Segment Performance Tracking
```typescript
interface SegmentAnalytics {
  segmentId: number;
  audienceCount: number;
  campaignCount: number;
  avgOpenRate: number;
  avgClickRate: number;
  growthHistory: {
    date: string;
    count: number;
  }[];
}
```

## 📧 Email Marketing System

### 1. Template Management

#### 1.1. API Endpoints
```typescript
Base URL: /v1/user/marketing/templates

GET    /                    # Lấy danh sách template
POST   /                    # Tạo template mới
GET    /:id                 # Lấy chi tiết template
PUT    /:id                 # Cập nhật template
DELETE /:id                 # Xóa template

// Template Operations
POST   /:id/duplicate       # Nhân bản template
POST   /:id/test-send       # Gửi test email
GET    /:id/preview         # Preview template
```

#### 1.2. Template Structure
```typescript
interface UserTemplateEmail {
  id: number;
  userId: number;
  name: string;            // Tên template
  subject: string;         // Tiêu đề email
  htmlContent: string;     // Nội dung HTML
  textContent?: string;    // Nội dung text (fallback)
  type: TemplateType;      // NEWSLETTER, PROMOTIONAL, TRANSACTIONAL
  previewText?: string;    // Text hiển thị trong inbox
  tags: string[];          // Tags để phân loại
  placeholders: string[];  // Available placeholders
  variableMetadata: any;   // Metadata cho variables
  status: 'DRAFT' | 'ACTIVE' | 'INACTIVE';
  createdAt: number;
  updatedAt: number;
}
```

#### 1.3. Placeholder System
```typescript
// Standard Placeholders
{{firstName}}              // Tên
{{lastName}}               // Họ
{{email}}                  // Email
{{phone}}                  // Số điện thoại
{{unsubscribeLink}}        // Link hủy đăng ký
{{trackingPixel}}          // Pixel tracking

// Custom Field Placeholders
{{customField.fieldName}}  // Trường tùy chỉnh

// System Placeholders
{{currentDate}}            // Ngày hiện tại
{{companyName}}            // Tên công ty
{{websiteUrl}}             // URL website
```

### 2. Campaign Management

#### 2.1. API Endpoints
```typescript
Base URL: /v1/user/marketing/campaigns

GET    /                    # Lấy danh sách campaign
POST   /                    # Tạo campaign mới
GET    /:id                 # Lấy chi tiết campaign
PUT    /:id                 # Cập nhật campaign
DELETE /:id                 # Xóa campaign

// Campaign Operations
POST   /:id/run             # Chạy campaign
POST   /:id/pause           # Tạm dừng campaign
POST   /:id/resume          # Tiếp tục campaign
POST   /:id/duplicate       # Nhân bản campaign
GET    /:id/analytics       # Thống kê campaign
```

#### 2.2. Campaign Types & Platforms
```typescript
enum CampaignPlatform {
  EMAIL = 'email',
  SMS = 'sms',
  ZALO = 'zalo',
  PUSH = 'push'
}

enum CampaignStatus {
  DRAFT = 'draft',
  SCHEDULED = 'scheduled',
  SENDING = 'sending',
  SENT = 'sent',
  PAUSED = 'paused',
  CANCELLED = 'cancelled',
  COMPLETED = 'completed'
}
```

#### 2.3. Campaign Execution Flow
```typescript
// 1. Campaign Creation
const campaign = await userCampaignService.create(userId, {
  title: 'Welcome Campaign',
  platform: 'email',
  segmentId: 123,
  templateId: 456,
  scheduledAt: 1640995200 // Unix timestamp
});

// 2. Audience Selection
- Từ segment: Tự động lấy audience theo criteria
- Manual selection: Chọn audience cụ thể
- Mixed: Kết hợp segment + manual selection

// 3. Queue Processing
- Tạo jobs cho từng audience
- Background processing với BullMQ
- Retry mechanism cho failed sends
- Progress tracking real-time
```

### 3. Email Campaign Worker System

#### 3.1. Queue Architecture
```typescript
// Queue Configuration
BullModule.registerQueue({
  name: QueueName.EMAIL_MARKETING,
})

// Job Structure
interface EmailMarketingJobDto {
  campaignId: number;
  audienceId: number;
  email: string;
  subject: string;
  content: string;
  customFields: Record<string, any>;
  server: EmailServerConfig;
  trackingId: string;
  createdAt: number;
}
```

#### 3.2. Worker Processing
```typescript
// Job Creation
const job = await emailMarketingQueue.add('send-email', jobData, {
  delay: scheduledDelay,     // Delay cho scheduled campaigns
  attempts: 3,               // Số lần retry
  backoff: {
    type: 'exponential',
    delay: 2000,
  },
  removeOnComplete: 100,     // Giữ 100 jobs completed
  removeOnFail: 50,         // Giữ 50 jobs failed
});

// Batch Processing
const jobs = audiences.map(audience => ({
  name: 'send-email',
  data: createJobData(audience),
  opts: jobOptions
}));

await emailMarketingQueue.addBulk(jobs);
```

#### 3.3. Tracking & Analytics
```typescript
// Email Events
enum EmailEvent {
  SENT = 'sent',
  DELIVERED = 'delivered',
  OPENED = 'opened',
  CLICKED = 'clicked',
  BOUNCED = 'bounced',
  UNSUBSCRIBED = 'unsubscribed',
  SPAM_REPORTED = 'spam_reported'
}

// Campaign Metrics
interface EmailCampaignStats {
  campaignId: number;
  sentCount: number;
  deliveredCount: number;
  openCount: number;
  clickCount: number;
  bounceCount: number;
  unsubscribeCount: number;

  // Calculated Rates
  deliveryRate: number;      // (delivered/sent) * 100
  openRate: number;          // (opened/delivered) * 100
  clickRate: number;         // (clicked/opened) * 100
  bounceRate: number;        // (bounced/sent) * 100
}
```

## 📱 Zalo Integration System (Đã Triển Khai Đầy Đủ)

### 1. Zalo Official Account Management

#### 1.1. API Endpoints
```typescript
Base URL: /v1/user/marketing/zalo

// Official Account Management
GET    /official-accounts           # Lấy danh sách OA
POST   /official-accounts/connect   # Kết nối OA mới
PUT    /official-accounts/:oaId     # Cập nhật thông tin OA
DELETE /official-accounts/:oaId     # Ngắt kết nối OA

// Follower Management
GET    /:oaId/followers             # Lấy danh sách follower
GET    /:oaId/followers/:userId     # Chi tiết follower
POST   /:oaId/followers/:userId/tag # Gắn tag cho follower
DELETE /:oaId/followers/:userId/tag # Bỏ tag follower

// Message Management
GET    /:oaId/messages              # Lịch sử tin nhắn
POST   /:oaId/messages              # Gửi tin nhắn
GET    /:oaId/conversations         # Danh sách cuộc hội thoại
```

#### 1.2. Zalo Services Architecture
```typescript
// Core Services
- ZaloService: Base Zalo API integration
- ZaloOaService: Official Account operations
- ZaloZnsService: Zalo Notification Service
- ZaloWebhookService: Webhook event handling
- ZaloAgentService: AI Agent integration

// Service Features
interface ZaloOfficialAccount {
  oaId: string;
  userId: number;           // User sở hữu
  name: string;            // Tên Official Account
  avatar: string;          // URL avatar
  description: string;     // Mô tả
  accessToken: string;     // Token truy cập (encrypted)
  refreshToken: string;    // Refresh token (encrypted)
  isActive: boolean;       // Trạng thái hoạt động
  connectedAt: number;     // Thời gian kết nối
  lastSyncAt: number;      // Lần sync cuối
}
```

#### 1.3. Follower Management
```typescript
interface ZaloFollower {
  id: number;
  oaId: string;
  userId: string;          // Zalo User ID
  displayName: string;     // Tên hiển thị
  avatar: string;          // URL avatar
  isFollowing: boolean;    // Đang theo dõi
  followedAt: number;      // Thời gian theo dõi
  lastInteractionAt: number; // Tương tác cuối
  tags: string[];          // Tags đã gắn
  customFields: Record<string, any>; // Thông tin tùy chỉnh
}

// Follower Operations
- Tự động sync follower từ Zalo API
- Gắn/bỏ tag cho follower
- Phân đoạn follower theo tiêu chí
- Tracking tương tác và engagement
```

### 2. Zalo Notification Service (ZNS)

#### 2.1. ZNS Template Management
```typescript
Base URL: /v1/user/marketing/zalo/:oaId/zns

GET    /templates                   # Lấy danh sách template ZNS
POST   /templates/register          # Đăng ký template mới
GET    /templates/:templateId       # Chi tiết template
PUT    /templates/:templateId       # Cập nhật template
DELETE /templates/:templateId       # Xóa template

// ZNS Message Operations
POST   /send                        # Gửi tin nhắn ZNS
GET    /messages                    # Lịch sử tin nhắn ZNS
GET    /messages/:messageId/status  # Trạng thái tin nhắn
```

#### 2.2. ZNS Template Structure
```typescript
interface ZaloZnsTemplate {
  templateId: string;      // ID template từ Zalo
  templateName: string;    // Tên template
  templateContent: string; // Nội dung template
  status: 'PENDING' | 'APPROVED' | 'REJECTED';
  price: number;          // Giá mỗi tin nhắn
  templateData: {
    [key: string]: string; // Placeholder data
  };
  createdAt: number;
  approvedAt?: number;
}
```

#### 2.3. ZNS Sending System
```typescript
// ZNS Message Structure
interface ZaloZnsMessage {
  phone: string;           // Số điện thoại nhận
  template_id: string;     // ID template
  template_data: Record<string, string>; // Dữ liệu thay thế
  tracking_id?: string;    // ID tracking
}

// Bulk ZNS Sending
interface BulkZnsRequest {
  templateId: string;
  segmentId?: number;      // Gửi theo segment
  audienceIds?: number[];  // Hoặc chọn audience cụ thể
  templateData: Record<string, string>;
}

// ZNS Analytics
interface ZnsAnalytics {
  totalSent: number;
  totalDelivered: number;
  totalFailed: number;
  deliveryRate: number;
  avgCost: number;
  totalCost: number;
}
```

### 3. Zalo Campaign Management

#### 3.1. Zalo Campaign API
```typescript
Base URL: /v1/user/marketing/zalo/:oaId/campaigns

GET    /                            # Lấy danh sách campaign
POST   /                            # Tạo campaign mới
GET    /:id                         # Chi tiết campaign
PUT    /:id                         # Cập nhật campaign
DELETE /:id                         # Xóa campaign

// Campaign Operations
POST   /:id/run                     # Chạy campaign
POST   /:id/pause                   # Tạm dừng
POST   /:id/resume                  # Tiếp tục
GET    /:id/logs                    # Lịch sử thực thi
```

#### 3.2. Zalo Campaign Types
```typescript
enum ZaloCampaignType {
  MESSAGE = 'message',     // Tin nhắn thường
  ZNS = 'zns',            // Zalo Notification Service
  BROADCAST = 'broadcast', // Tin nhắn quảng bá
  INTERACTIVE = 'interactive' // Tin nhắn tương tác
}

interface ZaloCampaign {
  id: number;
  oaId: string;
  userId: number;
  name: string;
  type: ZaloCampaignType;
  content: string;         // Nội dung tin nhắn
  templateId?: string;     // ZNS template ID
  segmentId?: number;      // Target segment
  scheduledAt?: number;    // Thời gian lên lịch
  status: CampaignStatus;
  createdAt: number;
}
```

### 4. Zalo Automation System

#### 4.1. Automation API
```typescript
Base URL: /v1/user/marketing/zalo/:oaId/automations

GET    /                            # Lấy danh sách automation
POST   /                            # Tạo automation mới
GET    /:id                         # Chi tiết automation
PUT    /:id                         # Cập nhật automation
DELETE /:id                         # Xóa automation

// Automation Control
POST   /:id/activate                # Kích hoạt
POST   /:id/deactivate              # Vô hiệu hóa
GET    /:id/logs                    # Lịch sử thực thi
```

#### 4.2. Automation Triggers
```typescript
enum ZaloTriggerType {
  NEW_FOLLOWER = 'new_follower',     // Follower mới
  MESSAGE_RECEIVED = 'message_received', // Nhận tin nhắn
  KEYWORD_MATCH = 'keyword_match',   // Khớp từ khóa
  USER_INTERACTION = 'user_interaction', // Tương tác user
  SCHEDULED = 'scheduled',           // Theo lịch
  WEBHOOK = 'webhook'                // Webhook event
}

interface ZaloAutomationTrigger {
  type: ZaloTriggerType;
  conditions: {
    keywords?: string[];     // Từ khóa trigger
    schedule?: string;       // Cron expression
    webhookUrl?: string;     // Webhook URL
  };
}
```

#### 4.3. Automation Actions
```typescript
enum ZaloActionType {
  SEND_MESSAGE = 'send_message',     // Gửi tin nhắn
  SEND_ZNS = 'send_zns',            // Gửi ZNS
  ADD_TAG = 'add_tag',              // Gắn tag
  REMOVE_TAG = 'remove_tag',        // Bỏ tag
  UPDATE_FIELD = 'update_field',    // Cập nhật field
  WEBHOOK = 'webhook',              // Gọi webhook
  WAIT = 'wait'                     // Chờ
}

interface ZaloAutomationAction {
  type: ZaloActionType;
  config: {
    message?: string;        // Nội dung tin nhắn
    templateId?: string;     // ZNS template
    tagName?: string;        // Tên tag
    fieldName?: string;      // Tên field
    fieldValue?: any;        // Giá trị field
    webhookUrl?: string;     // Webhook URL
    delay?: number;          // Thời gian chờ (phút)
  };
  conditions?: SegmentCriteria; // Điều kiện thực thi
}
```

### 5. Zalo Analytics & Reporting

#### 5.1. Analytics API
```typescript
Base URL: /v1/user/marketing/zalo/:oaId/analytics

GET    /overview                    # Tổng quan
GET    /followers                   # Thống kê follower
GET    /messages                    # Thống kê tin nhắn
GET    /campaigns                   # Thống kê campaign
GET    /zns                         # Thống kê ZNS
GET    /automations                 # Thống kê automation
```

#### 5.2. Zalo Metrics
```typescript
interface ZaloOverviewStats {
  totalFollowers: number;
  totalCampaigns: number;
  totalMessagesSent: number;
  totalZnsSent: number;

  // Growth Metrics
  followerGrowth: {
    date: string;
    newFollowers: number;
    unfollowers: number;
    netGrowth: number;
  }[];

  // Engagement Metrics
  messageStats: {
    sent: number;
    delivered: number;
    read: number;
    replied: number;
  };

  // ZNS Metrics
  znsStats: {
    sent: number;
    delivered: number;
    failed: number;
    totalCost: number;
  };
}
```

## 📊 Analytics & Reporting System

### 1. Marketing Overview Dashboard

#### 1.1. Overview API (Đã Triển Khai)
```typescript
Base URL: /v1/user/marketing/overview

GET    /                            # Tổng quan marketing
GET    /recent-templates             # Templates gần đây
GET    /statistics                  # Thống kê chi tiết
```

#### 1.2. Dashboard Metrics
```typescript
interface MarketingOverview {
  // Template Metrics
  totalTemplates: number;
  activeTemplates: number;
  draftTemplates: number;

  // Campaign Metrics
  totalCampaigns: number;
  activeCampaigns: number;
  completedCampaigns: number;

  // Email Metrics
  totalEmailsSent: number;
  openRate: number;        // Tỷ lệ mở (%)
  clickRate: number;       // Tỷ lệ click (%)
  bounceRate: number;      // Tỷ lệ bounce (%)

  // Audience Metrics
  totalAudiences: number;
  activeAudiences: number;
  segmentCount: number;

  // Growth Metrics
  growthData: {
    date: string;
    emails: number;
    opens: number;
    clicks: number;
  }[];
}
```

#### 1.3. Recent Activity
```typescript
interface RecentTemplate {
  id: number;
  name: string;
  subject: string;
  status: 'DRAFT' | 'ACTIVE' | 'INACTIVE';
  createdAt: number;
  lastUsed?: number;
  usageCount: number;
}

interface RecentCampaign {
  id: number;
  title: string;
  platform: string;
  status: string;
  sentCount: number;
  openRate: number;
  createdAt: number;
}
```

### 2. Advanced Analytics

#### 2.1. Campaign Performance
```typescript
interface CampaignAnalytics {
  campaignId: number;
  campaignName: string;
  platform: string;

  // Sending Metrics
  totalSent: number;
  totalDelivered: number;
  totalBounced: number;
  deliveryRate: number;

  // Engagement Metrics
  totalOpened: number;
  uniqueOpened: number;
  openRate: number;
  totalClicked: number;
  uniqueClicked: number;
  clickRate: number;

  // Time-based Analytics
  hourlyStats: {
    hour: number;
    opens: number;
    clicks: number;
  }[];

  // Device Analytics
  deviceStats: {
    device: string;
    opens: number;
    clicks: number;
  }[];

  // Geographic Analytics
  locationStats: {
    country: string;
    city: string;
    opens: number;
    clicks: number;
  }[];
}
```

#### 2.2. Audience Analytics
```typescript
interface AudienceAnalytics {
  // Audience Growth
  totalAudiences: number;
  activeAudiences: number;
  engagedAudiences: number;

  // Engagement Scoring
  engagementScore: {
    audienceId: number;
    email: string;
    score: number;        // 0-100
    lastEngagement: number;
    totalOpens: number;
    totalClicks: number;
  }[];

  // Segmentation Performance
  segmentPerformance: {
    segmentId: number;
    segmentName: string;
    audienceCount: number;
    avgOpenRate: number;
    avgClickRate: number;
    revenue?: number;
  }[];
}
```

### 3. Custom Reports

#### 3.1. Report Builder
```typescript
interface CustomReport {
  id: number;
  userId: number;
  name: string;
  description: string;

  // Report Configuration
  metrics: string[];       // Metrics to include
  dimensions: string[];    // Grouping dimensions
  filters: ReportFilter[]; // Data filters
  dateRange: {
    start: number;
    end: number;
    preset?: 'last_7_days' | 'last_30_days' | 'last_quarter';
  };

  // Scheduling
  schedule?: {
    frequency: 'daily' | 'weekly' | 'monthly';
    time: string;          // HH:mm format
    recipients: string[];   // Email recipients
    format: 'pdf' | 'excel' | 'csv';
  };

  createdAt: number;
  lastRunAt?: number;
}

interface ReportFilter {
  field: string;           // Field to filter
  operator: 'equals' | 'contains' | 'gt' | 'lt' | 'between';
  value: any;             // Filter value
}
```

#### 3.2. Export & Sharing
```typescript
// Export Options
interface ExportRequest {
  reportId: number;
  format: 'csv' | 'excel' | 'pdf';
  dateRange?: {
    start: number;
    end: number;
  };
  includeRawData: boolean;
  includeCharts: boolean;
}

// Report Sharing
interface SharedReport {
  id: number;
  reportId: number;
  shareToken: string;
  expiresAt: number;
  isPublic: boolean;
  allowedEmails?: string[];
  accessCount: number;
  lastAccessedAt?: number;
}
```

## 🔧 Advanced Marketing Features

### 1. Marketing Automation

#### 1.1. Workflow Builder
```typescript
interface MarketingWorkflow {
  id: number;
  userId: number;
  name: string;
  description: string;

  // Workflow Configuration
  trigger: WorkflowTrigger;
  steps: WorkflowStep[];
  isActive: boolean;

  // Execution Stats
  totalExecutions: number;
  successfulExecutions: number;
  failedExecutions: number;

  createdAt: number;
  lastExecutedAt?: number;
}

interface WorkflowTrigger {
  type: 'event' | 'schedule' | 'manual';
  config: {
    eventName?: string;    // API event name
    schedule?: string;     // Cron expression
    conditions?: SegmentCriteria;
  };
}

interface WorkflowStep {
  id: string;
  type: 'action' | 'condition' | 'delay' | 'split';
  config: any;
  nextSteps: string[];   // Next step IDs
  position: {
    x: number;
    y: number;
  };
}
```

#### 1.2. Drip Campaigns
```typescript
interface DripCampaign {
  id: number;
  userId: number;
  name: string;
  description: string;

  // Campaign Configuration
  triggerType: 'signup' | 'purchase' | 'custom_event';
  steps: DripStep[];
  isActive: boolean;

  // Enrollment
  totalEnrolled: number;
  activeEnrolled: number;
  completedEnrolled: number;

  createdAt: number;
}

interface DripStep {
  stepNumber: number;
  name: string;
  templateId: number;
  platform: 'email' | 'sms' | 'zalo';
  delayDays: number;      // Delay từ step trước
  delayHours: number;     // Delay hours
  conditions?: SegmentCriteria; // Điều kiện gửi

  // Step Performance
  sentCount: number;
  openCount: number;
  clickCount: number;
}
```

#### 1.3. Behavioral Triggers
```typescript
enum TriggerEvent {
  USER_SIGNUP = 'user_signup',
  EMAIL_OPENED = 'email_opened',
  EMAIL_CLICKED = 'email_clicked',
  LINK_CLICKED = 'link_clicked',
  PURCHASE_MADE = 'purchase_made',
  CART_ABANDONED = 'cart_abandoned',
  PROFILE_UPDATED = 'profile_updated',
  SUBSCRIPTION_CHANGED = 'subscription_changed'
}

interface BehavioralTrigger {
  id: number;
  userId: number;
  name: string;
  event: TriggerEvent;
  conditions: SegmentCriteria;
  actions: AutomationAction[];
  isActive: boolean;

  // Execution Stats
  triggerCount: number;
  executionCount: number;
  successRate: number;
}
```

### 2. A/B Testing System

#### 2.1. A/B Test Configuration
```typescript
interface ABTest {
  id: number;
  userId: number;
  campaignId: number;
  name: string;
  description: string;

  // Test Configuration
  testType: 'subject' | 'content' | 'send_time' | 'sender';
  variants: ABTestVariant[];
  trafficSplit: number[]; // [50, 50] for 50/50 split
  sampleSize: number;     // Total audience size

  // Success Criteria
  winnerCriteria: 'open_rate' | 'click_rate' | 'conversion_rate';
  confidenceLevel: number; // 95, 99
  minimumDetectableEffect: number; // 5%, 10%

  // Test Status
  status: 'draft' | 'running' | 'completed' | 'paused';
  startedAt?: number;
  completedAt?: number;
  winnerId?: string;

  createdAt: number;
}

interface ABTestVariant {
  id: string;
  name: string;
  description: string;

  // Variant Configuration
  templateId?: number;
  subject?: string;
  content?: string;
  sendTime?: string;
  senderName?: string;
  senderEmail?: string;

  // Performance Metrics
  sentCount: number;
  openCount: number;
  clickCount: number;
  conversionCount: number;

  // Calculated Metrics
  openRate: number;
  clickRate: number;
  conversionRate: number;
  confidenceInterval: {
    lower: number;
    upper: number;
  };
}
```

#### 2.2. Statistical Analysis
```typescript
interface ABTestResults {
  testId: number;
  status: 'running' | 'completed' | 'inconclusive';

  // Statistical Significance
  isSignificant: boolean;
  pValue: number;
  confidenceLevel: number;

  // Winner Analysis
  winnerId?: string;
  winnerLift: number;      // % improvement
  winnerConfidence: number; // Confidence in winner

  // Recommendations
  recommendation: 'continue_test' | 'declare_winner' | 'stop_test';
  recommendationReason: string;

  // Detailed Results
  variantResults: {
    variantId: string;
    sampleSize: number;
    conversionRate: number;
    standardError: number;
    confidenceInterval: {
      lower: number;
      upper: number;
    };
  }[];
}
```

### 3. Personalization Engine

#### 3.1. Dynamic Content
```typescript
interface PersonalizationRule {
  id: number;
  userId: number;
  name: string;
  description: string;

  // Rule Configuration
  conditions: SegmentCriteria;
  content: {
    subject?: string;
    htmlContent?: string;
    textContent?: string;
    images?: string[];
    ctaText?: string;
    ctaUrl?: string;
  };

  // Priority & Fallback
  priority: number;        // Higher number = higher priority
  fallbackContent?: any;   // Default content if no match

  // Performance
  matchCount: number;
  conversionCount: number;
  conversionRate: number;

  isActive: boolean;
  createdAt: number;
}
```

#### 3.2. Smart Recommendations
```typescript
interface SmartRecommendation {
  type: 'send_time' | 'subject_line' | 'content' | 'frequency';
  confidence: number;      // 0-100
  recommendation: string;
  reasoning: string;
  expectedImprovement: number; // % improvement

  // Supporting Data
  dataPoints: {
    metric: string;
    value: number;
    benchmark: number;
  }[];
}

// AI-Powered Features
interface AIInsights {
  audienceId: number;

  // Engagement Predictions
  openProbability: number;
  clickProbability: number;
  unsubscribeProbability: number;

  // Optimal Timing
  bestSendTime: {
    dayOfWeek: number;     // 0-6 (Sunday-Saturday)
    hour: number;          // 0-23
    confidence: number;
  };

  // Content Preferences
  preferredContentType: 'text' | 'image' | 'video' | 'mixed';
  preferredSubjectLength: number;
  preferredEmailLength: number;

  // Churn Risk
  churnRisk: 'low' | 'medium' | 'high';
  churnProbability: number;
  retentionRecommendations: string[];
}
```

## 🔗 Integration & Third-Party Services

### 1. Email Service Providers

#### 1.1. SMTP Configuration
```typescript
interface SMTPConfig {
  host: string;            // smtp.gmail.com, smtp.office365.com
  port: number;           // 465 (SSL), 587 (TLS), 25 (plain)
  secure: boolean;        // true for 465, false for other ports
  auth: {
    user: string;         // Email address
    pass: string;         // Password or app-specific password
  };
  tls?: {
    rejectUnauthorized: boolean;
  };
}

// Supported SMTP Providers
const SMTPProviders = {
  GMAIL: {
    host: 'smtp.gmail.com',
    port: 587,
    secure: false,
    requiresAppPassword: true
  },
  OUTLOOK: {
    host: 'smtp-mail.outlook.com',
    port: 587,
    secure: false,
    requiresAppPassword: false
  },
  YAHOO: {
    host: 'smtp.mail.yahoo.com',
    port: 587,
    secure: false,
    requiresAppPassword: true
  },
  CUSTOM: {
    host: 'custom',
    port: 587,
    secure: false,
    requiresAppPassword: false
  }
};
```

#### 1.2. Advanced Email Providers
```typescript
// SendGrid Integration
interface SendGridConfig {
  apiKey: string;
  fromEmail: string;
  fromName: string;
  replyTo?: string;
  templateEngine: boolean;
  trackingSettings: {
    clickTracking: boolean;
    openTracking: boolean;
    subscriptionTracking: boolean;
  };
}

// Mailgun Integration
interface MailgunConfig {
  apiKey: string;
  domain: string;
  region: 'us' | 'eu';
  fromEmail: string;
  fromName: string;
  trackingSettings: {
    opens: boolean;
    clicks: boolean;
    unsubscribes: boolean;
  };
}

// Amazon SES Integration
interface SESConfig {
  accessKeyId: string;
  secretAccessKey: string;
  region: string;
  fromEmail: string;
  fromName: string;
  configurationSet?: string;
}
```

### 2. SMS Service Providers

#### 2.1. Vietnamese SMS Providers
```typescript
// SpeedSMS (Vietnam)
interface SpeedSMSConfig {
  accessToken: string;
  brandName: string;      // Tên thương hiệu
  smsType: 1 | 2 | 3;    // 1: Quảng cáo, 2: Chăm sóc KH, 3: OTP
}

// FPT SMS (Vietnam)
interface FPTSMSConfig {
  username: string;
  password: string;
  brandName: string;
  serviceId: string;
  commandCode: string;
}

// Viettel SMS (Vietnam)
interface ViettelSMSConfig {
  username: string;
  password: string;
  cpCode: string;
  serviceId: string;
  commandCode: string;
}
```

#### 2.2. International SMS Providers
```typescript
// Twilio
interface TwilioConfig {
  accountSid: string;
  authToken: string;
  fromNumber: string;     // Twilio phone number
  messagingServiceSid?: string;
}

// Vonage (ex-Nexmo)
interface VonageConfig {
  apiKey: string;
  apiSecret: string;
  fromNumber: string;
  brand: string;
}

// AWS SNS
interface SNSConfig {
  accessKeyId: string;
  secretAccessKey: string;
  region: string;
  senderName: string;
}
```

### 3. Social Media Integration

#### 3.1. Facebook Integration
```typescript
interface FacebookConfig {
  appId: string;
  appSecret: string;
  accessToken: string;
  pageId: string;

  // Permissions
  permissions: string[];   // ['pages_manage_posts', 'pages_read_engagement']

  // Features
  features: {
    postToFeed: boolean;
    sendMessages: boolean;
    manageAds: boolean;
    accessInsights: boolean;
  };
}

// Facebook Marketing API
interface FacebookCampaign {
  id: number;
  facebookCampaignId: string;
  name: string;
  objective: string;      // REACH, TRAFFIC, CONVERSIONS
  status: string;         // ACTIVE, PAUSED, DELETED
  budget: number;
  targeting: {
    ageMin: number;
    ageMax: number;
    genders: number[];    // 1: male, 2: female
    locations: string[];
    interests: string[];
  };
}
```

#### 3.2. Instagram Integration
```typescript
interface InstagramConfig {
  accessToken: string;
  businessAccountId: string;

  // Content Types
  supportedContentTypes: string[]; // ['image', 'video', 'carousel', 'story']

  // Features
  features: {
    publishPosts: boolean;
    publishStories: boolean;
    accessInsights: boolean;
    manageComments: boolean;
  };
}
```

### 4. CRM Integration

#### 4.1. Salesforce Integration
```typescript
interface SalesforceConfig {
  clientId: string;
  clientSecret: string;
  username: string;
  password: string;
  securityToken: string;
  instanceUrl: string;

  // Sync Configuration
  syncSettings: {
    syncContacts: boolean;
    syncLeads: boolean;
    syncOpportunities: boolean;
    syncCampaigns: boolean;
    syncFrequency: 'realtime' | 'hourly' | 'daily';
  };
}

// Salesforce Data Mapping
interface SalesforceMapping {
  audienceToContact: {
    email: 'Email';
    phone: 'Phone';
    firstName: 'FirstName';
    lastName: 'LastName';
  };

  campaignToSalesforceCampaign: {
    name: 'Name';
    description: 'Description';
    status: 'Status';
    type: 'Type';
  };
}
```

#### 4.2. HubSpot Integration
```typescript
interface HubSpotConfig {
  apiKey: string;
  portalId: string;

  // Sync Settings
  syncSettings: {
    syncContacts: boolean;
    syncCompanies: boolean;
    syncDeals: boolean;
    syncEmailEvents: boolean;
    bidirectionalSync: boolean;
  };
}

// HubSpot Contact Sync
interface HubSpotContact {
  vid: number;            // HubSpot contact ID
  email: string;
  firstName: string;
  lastName: string;
  company: string;
  phone: string;
  lifecycleStage: string; // subscriber, lead, customer
  lastModifiedDate: number;

  // Custom Properties
  customProperties: Record<string, any>;
}
```

## 🔒 Security & Compliance

### 1. Data Protection & Privacy

#### 1.1. GDPR Compliance
```typescript
interface GDPRCompliance {
  // Consent Management
  consentTracking: {
    audienceId: number;
    consentType: 'email' | 'sms' | 'phone' | 'profiling';
    consentGiven: boolean;
    consentDate: number;
    consentSource: string;  // 'website', 'api', 'import'
    ipAddress: string;
    userAgent: string;
  };

  // Data Subject Rights
  dataSubjectRights: {
    rightToAccess: boolean;     // Export personal data
    rightToRectification: boolean; // Update personal data
    rightToErasure: boolean;    // Delete personal data
    rightToPortability: boolean; // Export in machine-readable format
    rightToObject: boolean;     // Opt-out of processing
  };

  // Data Processing Records
  processingRecords: {
    audienceId: number;
    processingPurpose: string;
    legalBasis: string;        // consent, legitimate_interest, contract
    dataCategories: string[];   // personal, sensitive, behavioral
    retentionPeriod: number;    // Days
    processingDate: number;
  };
}
```

#### 1.2. Data Encryption
```typescript
interface EncryptionConfig {
  // Field-level Encryption
  encryptedFields: {
    email: boolean;
    phone: boolean;
    customFields: boolean;
    apiKeys: boolean;
    accessTokens: boolean;
  };

  // Encryption Methods
  encryptionMethod: 'AES-256-GCM' | 'AES-256-CBC';
  keyRotationPeriod: number;   // Days

  // Key Management
  keyManagement: {
    provider: 'aws-kms' | 'azure-keyvault' | 'local';
    keyId: string;
    region?: string;
  };
}
```

### 2. Email Compliance

#### 2.1. CAN-SPAM Compliance
```typescript
interface CANSPAMCompliance {
  // Required Elements
  requiredElements: {
    unsubscribeLink: boolean;    // Must be present
    physicalAddress: boolean;    // Sender's physical address
    clearSenderInfo: boolean;    // Clear "From" information
    truthfulSubject: boolean;    // Subject line matches content
    advertisementLabel: boolean; // Label promotional emails
  };

  // Unsubscribe Handling
  unsubscribeHandling: {
    processWithin: number;       // Hours (max 10 days)
    honorFor: number;           // Days (min 30 days)
    noFeeRequired: boolean;     // Can't charge for unsubscribe
    simpleProcess: boolean;     // One-click unsubscribe
  };
}
```

#### 2.2. Deliverability Management
```typescript
interface DeliverabilityConfig {
  // Authentication
  authentication: {
    spf: boolean;               // SPF record configured
    dkim: boolean;              // DKIM signing enabled
    dmarc: boolean;             // DMARC policy set
    bimi: boolean;              // BIMI record (optional)
  };

  // Reputation Monitoring
  reputationMonitoring: {
    ipReputation: number;       // 0-100 score
    domainReputation: number;   // 0-100 score
    blacklistStatus: string[];  // Blacklist names
    feedbackLoops: string[];    // ISP feedback loops
  };

  // Bounce Handling
  bounceHandling: {
    hardBounceLimit: number;    // % before suppression
    softBounceLimit: number;    // Consecutive bounces
    suppressionListEnabled: boolean;
    autoCleanupEnabled: boolean;
  };
}
```

### 3. Rate Limiting & Quotas

#### 3.1. API Rate Limits
```typescript
interface RateLimitConfig {
  // Per-User Limits
  userLimits: {
    requestsPerMinute: number;   // API requests
    requestsPerHour: number;
    requestsPerDay: number;

    // Marketing Specific
    emailsPerHour: number;
    emailsPerDay: number;
    smsPerHour: number;
    smsPerDay: number;

    // Bulk Operations
    bulkImportPerDay: number;
    bulkExportPerDay: number;
  };

  // Global Limits
  globalLimits: {
    totalEmailsPerHour: number;
    totalSmsPerHour: number;
    concurrentCampaigns: number;
  };

  // Burst Protection
  burstProtection: {
    enabled: boolean;
    burstSize: number;          // Max burst requests
    burstWindow: number;        // Burst window (seconds)
    cooldownPeriod: number;     // Cooldown after burst
  };
}
```

#### 3.2. Resource Quotas
```typescript
interface ResourceQuotas {
  // Storage Quotas
  storage: {
    totalAudiences: number;
    totalTemplates: number;
    totalCampaigns: number;
    totalSegments: number;
    mediaStorageMB: number;
  };

  // Feature Quotas
  features: {
    automationWorkflows: number;
    abTests: number;
    customReports: number;
    integrations: number;
  };

  // Usage Tracking
  currentUsage: {
    audiences: number;
    templates: number;
    campaigns: number;
    segments: number;
    storageUsedMB: number;
  };
}
```

## 📱 Mobile & Multi-Channel Support

### 1. Mobile App Integration

#### 1.1. Push Notifications
```typescript
interface PushNotificationConfig {
  // Platform Configuration
  platforms: {
    ios: {
      bundleId: string;
      teamId: string;
      keyId: string;
      privateKey: string;     // P8 certificate
    };
    android: {
      projectId: string;
      privateKey: string;     // Firebase service account
      clientEmail: string;
    };
  };

  // Notification Types
  notificationTypes: {
    marketing: boolean;       // Promotional notifications
    transactional: boolean;   // Order updates, etc.
    behavioral: boolean;      // Abandoned cart, etc.
    news: boolean;           // News and updates
  };
}

interface PushCampaign {
  id: number;
  userId: number;
  title: string;
  message: string;

  // Targeting
  segmentId?: number;
  deviceTokens?: string[];
  platforms: ('ios' | 'android')[];

  // Rich Content
  imageUrl?: string;
  actionButtons?: {
    title: string;
    action: string;
    url?: string;
  }[];

  // Scheduling
  scheduledAt?: number;
  timezone: string;

  // Analytics
  sentCount: number;
  deliveredCount: number;
  openedCount: number;
  clickedCount: number;
}
```

#### 1.2. In-App Messaging
```typescript
interface InAppMessage {
  id: number;
  userId: number;
  name: string;

  // Message Configuration
  messageType: 'banner' | 'modal' | 'fullscreen' | 'tooltip';
  content: {
    title: string;
    message: string;
    imageUrl?: string;
    ctaText?: string;
    ctaUrl?: string;
  };

  // Targeting
  triggers: {
    eventName: string;       // App event that triggers message
    conditions: SegmentCriteria;
    frequency: 'once' | 'daily' | 'session' | 'always';
  };

  // Display Settings
  displaySettings: {
    position: 'top' | 'center' | 'bottom';
    animation: 'slide' | 'fade' | 'bounce';
    duration: number;        // Auto-dismiss after seconds
    dismissible: boolean;
  };

  // Performance
  impressions: number;
  clicks: number;
  dismissals: number;
  conversions: number;
}
```

### 2. Multi-Channel Campaigns

#### 2.1. Cross-Channel Orchestration
```typescript
interface MultiChannelCampaign {
  id: number;
  userId: number;
  name: string;
  description: string;

  // Channel Configuration
  channels: {
    email: {
      enabled: boolean;
      templateId: number;
      priority: number;       // 1-5 (1 = highest)
      delay: number;          // Minutes after trigger
    };
    sms: {
      enabled: boolean;
      message: string;
      priority: number;
      delay: number;
    };
    push: {
      enabled: boolean;
      title: string;
      message: string;
      priority: number;
      delay: number;
    };
    zalo: {
      enabled: boolean;
      templateId?: string;
      message: string;
      priority: number;
      delay: number;
    };
  };

  // Execution Strategy
  strategy: 'parallel' | 'sequential' | 'fallback';
  fallbackRules: {
    channel: string;
    condition: 'not_delivered' | 'not_opened' | 'not_clicked';
    waitTime: number;        // Minutes to wait before fallback
  }[];

  // Frequency Capping
  frequencyCapping: {
    maxEmailsPerDay: number;
    maxSmsPerDay: number;
    maxPushPerDay: number;
    respectGlobalLimits: boolean;
  };
}
```

#### 2.2. Channel Performance Comparison
```typescript
interface ChannelPerformance {
  campaignId: number;

  // Per-Channel Metrics
  channelMetrics: {
    channel: string;
    sent: number;
    delivered: number;
    opened: number;
    clicked: number;
    converted: number;

    // Calculated Rates
    deliveryRate: number;
    openRate: number;
    clickRate: number;
    conversionRate: number;

    // Cost Metrics
    cost: number;
    costPerClick: number;
    costPerConversion: number;
    roi: number;
  }[];

  // Cross-Channel Analysis
  crossChannelMetrics: {
    totalReach: number;        // Unique users reached
    totalFrequency: number;    // Avg messages per user
    channelOverlap: {
      channels: string[];
      userCount: number;
    }[];

    // Attribution
    firstTouchAttribution: {
      channel: string;
      conversions: number;
    }[];
    lastTouchAttribution: {
      channel: string;
      conversions: number;
    }[];
  };
}
```

## 🚀 Future Roadmap & Advanced Features

### 1. AI-Powered Marketing

#### 1.1. Predictive Analytics
```typescript
interface PredictiveAnalytics {
  // Customer Lifetime Value
  clvPrediction: {
    audienceId: number;
    predictedClv: number;
    confidence: number;
    timeframe: number;       // Days
    factors: {
      factor: string;
      importance: number;    // 0-1
    }[];
  };

  // Churn Prediction
  churnPrediction: {
    audienceId: number;
    churnProbability: number;
    riskLevel: 'low' | 'medium' | 'high';
    timeToChurn: number;     // Days
    preventionRecommendations: string[];
  };

  // Engagement Prediction
  engagementPrediction: {
    audienceId: number;
    campaignId: number;
    openProbability: number;
    clickProbability: number;
    conversionProbability: number;
    optimalSendTime: {
      dayOfWeek: number;
      hour: number;
      confidence: number;
    };
  };
}
```

#### 1.2. Content Optimization
```typescript
interface ContentOptimization {
  // Subject Line Optimization
  subjectLineOptimization: {
    originalSubject: string;
    optimizedSubjects: {
      subject: string;
      predictedOpenRate: number;
      confidence: number;
      reasoning: string;
    }[];
    recommendations: string[];
  };

  // Content Personalization
  contentPersonalization: {
    audienceId: number;
    personalizedContent: {
      section: string;        // header, body, cta
      originalContent: string;
      personalizedContent: string;
      personalizationReason: string;
      expectedLift: number;   // % improvement
    }[];
  };

  // Send Time Optimization
  sendTimeOptimization: {
    audienceSegment: string;
    optimalTimes: {
      dayOfWeek: number;
      hour: number;
      expectedOpenRate: number;
      expectedClickRate: number;
      confidence: number;
    }[];
    globalOptimalTime: {
      dayOfWeek: number;
      hour: number;
      timezone: string;
    };
  };
}
```

### 2. Advanced Integrations

#### 2.1. E-commerce Integration
```typescript
interface EcommerceIntegration {
  // Platform Connections
  platforms: {
    shopify: {
      shopDomain: string;
      accessToken: string;
      webhookEndpoints: string[];
    };
    woocommerce: {
      siteUrl: string;
      consumerKey: string;
      consumerSecret: string;
    };
    magento: {
      baseUrl: string;
      accessToken: string;
      storeCode: string;
    };
  };

  // Product Recommendations
  productRecommendations: {
    audienceId: number;
    recommendedProducts: {
      productId: string;
      productName: string;
      price: number;
      imageUrl: string;
      recommendationScore: number;
      recommendationReason: string;
    }[];
    algorithm: 'collaborative' | 'content_based' | 'hybrid';
  };

  // Abandoned Cart Recovery
  abandonedCartRecovery: {
    cartId: string;
    customerId: string;
    cartValue: number;
    items: {
      productId: string;
      quantity: number;
      price: number;
    }[];
    recoverySequence: {
      stepNumber: number;
      delayHours: number;
      templateId: number;
      discountOffer?: {
        type: 'percentage' | 'fixed';
        value: number;
      };
    }[];
  };
}
```

#### 2.2. Analytics Platform Integration
```typescript
interface AnalyticsIntegration {
  // Google Analytics
  googleAnalytics: {
    trackingId: string;
    measurementId: string;   // GA4
    apiKey: string;

    // Event Tracking
    eventTracking: {
      emailOpened: boolean;
      emailClicked: boolean;
      campaignConversion: boolean;
      customEvents: {
        eventName: string;
        parameters: Record<string, any>;
      }[];
    };
  };

  // Facebook Pixel
  facebookPixel: {
    pixelId: string;
    accessToken: string;

    // Conversion Tracking
    conversionEvents: {
      eventName: string;
      value?: number;
      currency?: string;
      customData?: Record<string, any>;
    }[];
  };

  // Custom Analytics
  customAnalytics: {
    webhookUrl: string;
    events: string[];
    authentication: {
      type: 'bearer' | 'api_key' | 'basic';
      credentials: Record<string, string>;
    };
  };
}
```

## 📊 Performance Monitoring & Optimization

### 1. System Performance

#### 1.1. Queue Monitoring
```typescript
interface QueueMonitoring {
  // Queue Health
  queueHealth: {
    queueName: string;
    activeJobs: number;
    waitingJobs: number;
    completedJobs: number;
    failedJobs: number;

    // Performance Metrics
    avgProcessingTime: number;  // Milliseconds
    throughputPerMinute: number;
    errorRate: number;          // %

    // Alerts
    alerts: {
      type: 'high_queue_length' | 'high_error_rate' | 'slow_processing';
      threshold: number;
      currentValue: number;
      severity: 'warning' | 'critical';
    }[];
  };

  // Worker Performance
  workerPerformance: {
    workerId: string;
    jobsProcessed: number;
    avgProcessingTime: number;
    errorCount: number;
    lastHeartbeat: number;
    status: 'active' | 'idle' | 'error' | 'offline';
  }[];
}
```

#### 1.2. Database Performance
```typescript
interface DatabasePerformance {
  // Query Performance
  queryPerformance: {
    queryType: string;
    avgExecutionTime: number;
    slowQueries: {
      query: string;
      executionTime: number;
      frequency: number;
    }[];

    // Index Usage
    indexUsage: {
      tableName: string;
      indexName: string;
      usageCount: number;
      efficiency: number;     // %
    }[];
  };

  // Connection Pool
  connectionPool: {
    totalConnections: number;
    activeConnections: number;
    idleConnections: number;
    waitingQueries: number;
    avgWaitTime: number;
  };

  // Storage Metrics
  storageMetrics: {
    totalSize: number;        // MB
    dataSize: number;         // MB
    indexSize: number;        // MB
    growthRate: number;       // MB/day

    // Table Sizes
    tableSizes: {
      tableName: string;
      rowCount: number;
      sizeMB: number;
    }[];
  };
}
```

### 2. Application Monitoring

#### 2.1. API Performance
```typescript
interface APIPerformance {
  // Endpoint Metrics
  endpointMetrics: {
    endpoint: string;
    method: string;
    requestCount: number;
    avgResponseTime: number;
    errorRate: number;

    // Response Time Percentiles
    p50ResponseTime: number;
    p95ResponseTime: number;
    p99ResponseTime: number;

    // Error Breakdown
    errorBreakdown: {
      statusCode: number;
      count: number;
      percentage: number;
    }[];
  }[];

  // Rate Limiting
  rateLimitingMetrics: {
    userId: number;
    endpoint: string;
    requestCount: number;
    limitHit: boolean;
    remainingQuota: number;
    resetTime: number;
  }[];
}
```

#### 2.2. User Experience Monitoring
```typescript
interface UserExperienceMonitoring {
  // Page Load Times
  pageLoadTimes: {
    page: string;
    avgLoadTime: number;
    p95LoadTime: number;
    bounceRate: number;
  }[];

  // Feature Usage
  featureUsage: {
    feature: string;
    usageCount: number;
    uniqueUsers: number;
    avgSessionDuration: number;
    completionRate: number;
  }[];

  // Error Tracking
  errorTracking: {
    errorType: string;
    errorMessage: string;
    frequency: number;
    affectedUsers: number;
    firstSeen: number;
    lastSeen: number;
  }[];
}
```

## 🎯 Kết Luận

Tài liệu này cung cấp một cái nhìn toàn diện về hệ thống marketing cho user, bao gồm:

### ✅ Đã Triển Khai
- **Core Marketing Features**: Tag, Audience, Segment, Campaign, Template management
- **Email Marketing**: Template system, campaign execution, worker processing
- **Zalo Integration**: Đầy đủ tính năng OA, ZNS, automation, analytics
- **Analytics Dashboard**: Overview, statistics, reporting
- **Queue System**: BullMQ integration cho background processing

### 🚧 Đang Phát Triển
- **SMS Integration**: Multiple provider support
- **Advanced Analytics**: Predictive analytics, AI insights
- **A/B Testing**: Statistical analysis, winner determination
- **Multi-channel Campaigns**: Cross-channel orchestration

### 🔮 Roadmap Tương Lai
- **AI-Powered Features**: Content optimization, send time optimization
- **Advanced Integrations**: E-commerce, CRM, social media
- **Mobile Features**: Push notifications, in-app messaging
- **Enterprise Features**: Advanced security, compliance, monitoring

Hệ thống được thiết kế với kiến trúc modular, scalable và có thể mở rộng để đáp ứng nhu cầu phát triển trong tương lai.