# 📧 Rules Đầy Đủ Cho Email Marketing User

> T<PERSON><PERSON> liệu chi tiết về tất cả các quy tắc phát triển cho hệ thống email marketing của user

## 🏗️ Kiến Trúc Hệ Thống Email Marketing

### 1. <PERSON><PERSON><PERSON>r<PERSON>dule
```
src/modules/marketing/user/
├── entities/
│   ├── user-template-email.entity.ts      # Template email
│   ├── user-campaign.entity.ts            # Campaign
│   ├── user-campaign-history.entity.ts    # Lịch sử gửi
│   ├── user-audience.entity.ts            # Khách hàng
│   └── user-segment.entity.ts             # Phân đoạn
├── services/
│   ├── user-template-email.service.ts     # Quản lý template
│   ├── email-marketing.service.ts         # Xử lý campaign
│   └── user-campaign.service.ts           # Quản lý campaign
├── controllers/
│   ├── user-template-email.controller.ts  # API template
│   └── email-campaign.controller.ts       # API campaign
├── dto/
│   ├── template-email/                    # DTOs template
│   └── email-campaign/                    # DTOs campaign
└── queue-integration-guide.md             # Hướng dẫn worker
```

### 2. <PERSON><PERSON><PERSON> Worker System
```typescript
// Queue Configuration
BullModule.registerQueue({
  name: QueueName.EMAIL_MARKETING,
})

// Job Processing
interface EmailMarketingJobDto {
  campaignId: number;
  audienceId: number;
  email: string;
  subject: string;
  content: string;
  customFields: Record<string, any>;
  server?: EmailServerConfig;
  trackingId: string;
  createdAt: number;
}
```

### 3. Email Server Integration
```typescript
// SMTP Configuration từ Integration Module
interface EmailServerConfiguration {
  id: number;
  userId: number;
  serverName: string;
  host: string;              // smtp.gmail.com
  port: number;             // 587, 465
  username: string;         // Email account
  password: string;         // Encrypted password
  useSsl: boolean;          // SSL/TLS
  useStartTls: boolean;     // STARTTLS
  isActive: boolean;
  additionalSettings: any;
}
```

## 📝 Entity Rules

### 1. UserTemplateEmail Entity

#### 1.1. Cấu Trúc Bảng
```sql
CREATE TABLE user_template_email (
  id BIGSERIAL PRIMARY KEY,
  user_id BIGINT,
  name VARCHAR(255),
  subject VARCHAR(255),
  content TEXT,                    -- HTML content
  text_content TEXT,               -- Plain text (optional)
  type VARCHAR(50) DEFAULT 'NEWSLETTER',
  preview_text VARCHAR(255),       -- Inbox preview
  tags JSONB,                      -- Array of tags
  placeholders JSON,               -- Variable names
  variable_metadata JSONB,         -- Variable details
  status VARCHAR(20) DEFAULT 'DRAFT',
  created_at BIGINT,
  updated_at BIGINT
);
```

#### 1.2. Business Rules
```typescript
// Template Types
enum TemplateType {
  NEWSLETTER = 'NEWSLETTER',
  PROMOTIONAL = 'PROMOTIONAL', 
  TRANSACTIONAL = 'TRANSACTIONAL',
  WELCOME = 'WELCOME',
  ABANDONED_CART = 'ABANDONED_CART',
  FOLLOW_UP = 'FOLLOW_UP'
}

// Template Status
enum TemplateStatus {
  DRAFT = 'DRAFT',
  ACTIVE = 'ACTIVE',
  ARCHIVED = 'ARCHIVED'
}

// Validation Rules
- name: required, max 255 chars, unique per user
- subject: required, max 255 chars
- htmlContent: required (mapped to 'content' column)
- textContent: optional, fallback for HTML
- type: default 'NEWSLETTER'
- status: default 'DRAFT'
- tags: array of strings, auto-cleaned
- placeholders: auto-extracted from content
- variableMetadata: detailed variable info
```

#### 1.3. Placeholder System
```typescript
// Auto-extraction từ HTML content
const placeholders = TemplateEmailUtils.extractPlaceholders(htmlContent);
// Kết quả: ['firstName', 'lastName', 'companyName']

// Variable Metadata Structure
interface VariableMetadata {
  [variableName: string]: {
    type: 'TEXT' | 'NUMBER' | 'DATE' | 'URL' | 'IMAGE';
    defaultValue?: string;
    required?: boolean;
    description?: string;
  };
}

// Standard Placeholders
{{firstName}}           // Tên
{{lastName}}            // Họ  
{{email}}              // Email
{{phone}}              // Số điện thoại
{{unsubscribeLink}}    // Link hủy đăng ký
{{trackingPixel}}      // Pixel tracking
{{customField.name}}   // Custom field
```

### 2. UserCampaign Entity

#### 2.1. Cấu Trúc Bảng
```sql
CREATE TABLE user_campaigns (
  id BIGSERIAL PRIMARY KEY,
  user_id BIGINT,
  title VARCHAR(255),
  description TEXT,
  platform VARCHAR(255),          -- 'email', 'sms', 'zalo'
  content TEXT,                    -- HTML content
  server JSONB,                    -- SMTP config
  scheduled_at BIGINT,             -- Unix timestamp
  subject VARCHAR(255),            -- Email subject
  status VARCHAR(20),              -- Campaign status
  segment_id BIGINT,               -- Target segment
  audience_ids JSONB,              -- Manual audience selection
  created_at BIGINT,
  updated_at BIGINT
);
```

#### 2.2. Campaign Status Flow
```typescript
enum CampaignStatus {
  DRAFT = 'draft',
  SCHEDULED = 'scheduled',
  SENDING = 'sending', 
  SENT = 'sent',
  PAUSED = 'paused',
  CANCELLED = 'cancelled',
  COMPLETED = 'completed'
}

// Status Transitions
DRAFT → SCHEDULED → SENDING → SENT → COMPLETED
DRAFT → SENDING → SENT → COMPLETED
SCHEDULED → CANCELLED
SENDING → PAUSED → SENDING
```

#### 2.3. Audience Selection Rules
```typescript
// Validation Logic
if (!segmentId && (!audienceIds || audienceIds.length === 0)) {
  throw new BadRequestException('Phải chọn segment hoặc danh sách audience');
}

// Priority: Segment > Manual Selection
if (segmentId) {
  // Lấy audience từ segment criteria
  audiences = await segmentService.getAudiencesInSegment(segmentId);
} else {
  // Lấy audience theo IDs
  audiences = await audienceRepository.findByIds(audienceIds);
}
```

### 3. UserCampaignHistory Entity

#### 3.1. Tracking Email Events
```sql
CREATE TABLE user_campaign_history (
  id BIGSERIAL PRIMARY KEY,
  campaign_id BIGINT,
  audience_id BIGINT,
  status VARCHAR(20),              -- Email status
  sent_at BIGINT,                  -- Send timestamp
  created_at BIGINT
);
```

#### 3.2. Email Status Values
```typescript
enum SendStatus {
  PENDING = 'PENDING',
  SENT = 'SENT',
  DELIVERED = 'DELIVERED',
  OPENED = 'OPENED',
  CLICKED = 'CLICKED',
  BOUNCED = 'BOUNCED',
  FAILED = 'FAILED',
  UNSUBSCRIBED = 'UNSUBSCRIBED'
}

// Status Progression
PENDING → SENT → DELIVERED → OPENED → CLICKED
PENDING → SENT → BOUNCED
PENDING → FAILED
```

## 🎯 Service Layer Rules

### 1. UserTemplateEmailService

#### 1.1. CRUD Operations
```typescript
@Injectable()
export class UserTemplateEmailService {
  // Create Template
  async create(userId: number, createDto: CreateTemplateEmailDto): Promise<UserTemplateEmail> {
    // 1. Validate input data
    await this.validateCreateData(userId, createDto);
    
    // 2. Clean tags
    const cleanedTags = TemplateEmailUtils.cleanTags(createDto.tags);
    
    // 3. Extract placeholders
    const placeholders = TemplateEmailUtils.extractPlaceholders(createDto.htmlContent);
    
    // 4. Process variable metadata
    const variableMetadata = TemplateEmailUtils.processVariableMetadata(createDto.variables);
    
    // 5. Save to database
    return await this.repository.save(templateData);
  }
  
  // Validation Rules
  private async validateCreateData(userId: number, createDto: CreateTemplateEmailDto): Promise<void> {
    // Check name uniqueness per user
    const existingTemplate = await this.repository.findByUserIdAndName(userId, createDto.name);
    if (existingTemplate) {
      throw new ConflictException('Tên template đã tồn tại');
    }
    
    // Validate HTML content
    if (!createDto.htmlContent && !createDto.content) {
      throw new BadRequestException('htmlContent hoặc content là bắt buộc');
    }
  }
}
```

#### 1.2. Template Utils
```typescript
export class TemplateEmailUtils {
  // Extract placeholders từ HTML
  static extractPlaceholders(htmlContent: string): string[] {
    const regex = /\{\{([^}]+)\}\}/g;
    const matches = [];
    let match;
    
    while ((match = regex.exec(htmlContent)) !== null) {
      matches.push(match[1].trim());
    }
    
    return [...new Set(matches)]; // Remove duplicates
  }
  
  // Clean tags array
  static cleanTags(tags: string[]): string[] {
    return tags
      .map(tag => tag.trim())
      .filter(tag => tag.length > 0)
      .filter((tag, index, arr) => arr.indexOf(tag) === index); // Remove duplicates
  }
  
  // Process variable metadata
  static processVariableMetadata(variables?: EmailVariableDto[]): Record<string, any> {
    if (!variables) return {};
    
    const metadata = {};
    variables.forEach(variable => {
      metadata[variable.name] = {
        type: variable.type,
        defaultValue: variable.defaultValue,
        required: variable.required,
        description: variable.description
      };
    });
    
    return metadata;
  }
}
```

### 2. EmailMarketingService

#### 2.1. Campaign Creation & Queue Integration
```typescript
@Injectable()
export class EmailMarketingService {
  constructor(
    @InjectQueue(QueueName.EMAIL_MARKETING)
    private emailMarketingQueue: Queue
  ) {}
  
  @Transactional()
  async createEmailCampaign(userId: number, createDto: CreateEmailCampaignDto): Promise<CreateEmailCampaignResponseDto> {
    // 1. Validate input
    this.validateCampaignInput(createDto);
    
    // 2. Create campaign
    const campaign = await this.createCampaignRecord(userId, createDto);
    
    // 3. Get target audiences
    const audiences = await this.getAudiencesForCampaign(userId, campaign);
    
    // 4. Create queue jobs
    const jobIds = await this.createEmailJobs(campaign, audiences);
    
    return {
      campaignId: campaign.id,
      jobCount: jobIds.length,
      jobIds,
      scheduledAt: campaign.scheduledAt,
      status: campaign.status
    };
  }
  
  // Job Creation
  private async createEmailJobs(campaign: UserCampaign, audiences: UserAudience[]): Promise<string[]> {
    const jobIds: string[] = [];
    const now = Date.now();
    
    for (const audience of audiences) {
      // Get custom fields
      const customFields = await this.getAudienceCustomFields(audience.id);
      
      // Create tracking ID
      const trackingId = this.generateTrackingId(campaign.id, audience.id);
      
      // Create job data
      const jobData: EmailMarketingJobDto = {
        campaignId: campaign.id,
        audienceId: audience.id,
        email: audience.email,
        subject: campaign.subject,
        content: campaign.content,
        customFields,
        server: campaign.server,
        trackingId,
        createdAt: now
      };
      
      // Calculate delay for scheduled campaigns
      const delay = campaign.scheduledAt ? 
        Math.max(0, campaign.scheduledAt * 1000 - now) : 0;
      
      // Add job to queue
      const job = await this.emailMarketingQueue.add(
        EmailMarketingJobName.SEND_EMAIL, 
        jobData, 
        {
          delay,
          attempts: 3,
          backoff: { type: 'exponential', delay: 2000 },
          removeOnComplete: 100,
          removeOnFail: 50
        }
      );
      
      jobIds.push(job.id.toString());
    }
    
    return jobIds;
  }
}
```

#### 2.2. Analytics & Reporting
```typescript
// Campaign Analytics
async getCampaignAnalytics(campaignId: number): Promise<EmailCampaignAnalytics> {
  const campaign = await this.userCampaignRepository.findOne({ where: { id: campaignId } });
  const history = await this.userCampaignHistoryRepository.find({ 
    where: { campaignId } 
  });
  
  // Calculate metrics
  const totalSent = history.filter(h => h.status === SendStatus.SENT).length;
  const totalOpened = history.filter(h => h.status === SendStatus.OPENED).length;
  const totalClicked = history.filter(h => h.status === SendStatus.CLICKED).length;
  const totalBounced = history.filter(h => h.status === SendStatus.BOUNCED).length;
  
  // Calculate rates
  const openRate = totalSent > 0 ? (totalOpened / totalSent) * 100 : 0;
  const clickRate = totalOpened > 0 ? (totalClicked / totalOpened) * 100 : 0;
  const bounceRate = totalSent > 0 ? (totalBounced / totalSent) * 100 : 0;
  
  return {
    campaignId,
    totalSent,
    totalOpened,
    totalClicked,
    totalBounced,
    openRate: Math.round(openRate * 10) / 10,
    clickRate: Math.round(clickRate * 10) / 10,
    bounceRate: Math.round(bounceRate * 10) / 10
  };
}
```

## 🔧 Controller Layer Rules

### 1. UserTemplateEmailController

#### 1.1. API Endpoints
```typescript
@ApiTags(SWAGGER_API_TAGS.USER_TEMPLATE_EMAIL)
@Controller('v1/user/marketing/templates')
@UseGuards(JwtUserGuard)
@ApiBearerAuth()
export class UserTemplateEmailController {
  
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách template email' })
  async findAll(
    @CurrentUser() user: JwtPayload,
    @Query() queryDto: TemplateEmailQueryDto
  ): Promise<ApiResponseDto<PaginatedResult<UserTemplateEmail>>> {
    const result = await this.service.findAll(user.id, queryDto);
    return ApiResponseDto.success(result, 'Lấy danh sách template email thành công');
  }
  
  @Post()
  @ApiOperation({ summary: 'Tạo template email mới' })
  async create(
    @CurrentUser() user: JwtPayload,
    @Body() createDto: CreateTemplateEmailDto
  ): Promise<ApiResponseDto<UserTemplateEmail>> {
    const result = await this.service.create(user.id, createDto);
    return ApiResponseDto.success(result, 'Tạo template email thành công');
  }
}
```

#### 1.2. Swagger Documentation
```typescript
// Response Examples
@ApiResponse({
  status: 200,
  description: 'Danh sách template email',
  schema: {
    allOf: [
      { $ref: getSchemaPath(ApiResponseDto) },
      {
        properties: {
          data: {
            allOf: [
              { $ref: getSchemaPath(PaginatedResult) },
              {
                properties: {
                  data: {
                    type: 'array',
                    items: { $ref: getSchemaPath(TemplateEmailResponseDto) }
                  }
                }
              }
            ]
          }
        }
      }
    ]
  }
})
```

### 2. EmailCampaignController

#### 2.1. Campaign Management APIs
```typescript
@ApiTags(SWAGGER_API_TAGS.EMAIL_CAMPAIGN)
@Controller('v1/user/marketing/email-campaigns')
@UseGuards(JwtUserGuard)
@ApiBearerAuth()
export class EmailCampaignController {
  
  @Post()
  @ApiOperation({ summary: 'Tạo chiến dịch email marketing' })
  async createEmailCampaign(
    @CurrentUser() user: JwtPayload,
    @Body() createDto: CreateEmailCampaignDto
  ): Promise<AppApiResponse<CreateEmailCampaignResponseDto>> {
    const result = await this.emailMarketingService.createEmailCampaign(user.id, createDto);
    return AppApiResponse.success(result, 'Tạo chiến dịch email thành công');
  }
  
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách chiến dịch email' })
  async getEmailCampaigns(
    @CurrentUser() user: JwtPayload,
    @Query() queryDto: EmailCampaignQueryDto
  ): Promise<AppApiResponse<PaginatedResult<RecentCampaignDto>>> {
    const result = await this.emailMarketingService.getRecentCampaignsPaginated(user.id, queryDto);
    return AppApiResponse.paginated(result, 'Lấy danh sách chiến dịch email thành công');
  }
}
```

## 📊 DTO Rules

### 1. Template Email DTOs

#### 1.1. CreateTemplateEmailDto
```typescript
export class CreateTemplateEmailDto {
  @IsNotEmpty({ message: 'Tên template không được để trống' })
  @IsString({ message: 'Tên template phải là chuỗi' })
  @MaxLength(255, { message: 'Tên template không được vượt quá 255 ký tự' })
  name: string;

  @IsNotEmpty({ message: 'Tiêu đề email không được để trống' })
  @IsString({ message: 'Tiêu đề email phải là chuỗi' })
  @MaxLength(255, { message: 'Tiêu đề email không được vượt quá 255 ký tự' })
  subject: string;

  @IsOptional()
  @IsString({ message: 'Nội dung HTML phải là chuỗi' })
  htmlContent?: string;

  @IsOptional()
  @IsString({ message: 'Nội dung text phải là chuỗi' })
  textContent?: string;

  @IsOptional()
  @IsEnum(TemplateType, { message: 'Loại template không hợp lệ' })
  type?: TemplateType;

  @IsOptional()
  @IsString({ message: 'Preview text phải là chuỗi' })
  @MaxLength(255, { message: 'Preview text không được vượt quá 255 ký tự' })
  previewText?: string;

  @IsOptional()
  @IsArray({ message: 'Tags phải là mảng' })
  @IsString({ each: true, message: 'Mỗi tag phải là chuỗi' })
  tags?: string[];

  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => EmailVariableDto)
  variables?: EmailVariableDto[];
}
```

#### 1.2. EmailVariableDto
```typescript
export class EmailVariableDto {
  @IsNotEmpty({ message: 'Tên biến không được để trống' })
  @IsString({ message: 'Tên biến phải là chuỗi' })
  name: string;

  @IsEnum(['TEXT', 'NUMBER', 'DATE', 'URL', 'IMAGE'], { 
    message: 'Loại biến phải là TEXT, NUMBER, DATE, URL hoặc IMAGE' 
  })
  type: 'TEXT' | 'NUMBER' | 'DATE' | 'URL' | 'IMAGE';

  @IsOptional()
  @IsString({ message: 'Giá trị mặc định phải là chuỗi' })
  defaultValue?: string;

  @IsOptional()
  @IsBoolean({ message: 'Required phải là boolean' })
  required?: boolean;

  @IsOptional()
  @IsString({ message: 'Mô tả phải là chuỗi' })
  description?: string;
}
```

### 2. Email Campaign DTOs

#### 2.1. CreateEmailCampaignDto
```typescript
export class CreateEmailCampaignDto {
  @IsNotEmpty({ message: 'Tiêu đề chiến dịch không được để trống' })
  @IsString({ message: 'Tiêu đề chiến dịch phải là chuỗi' })
  @MaxLength(255, { message: 'Tiêu đề chiến dịch không được vượt quá 255 ký tự' })
  title: string;

  @IsOptional()
  @IsString({ message: 'Mô tả phải là chuỗi' })
  description?: string;

  @IsNotEmpty({ message: 'Tiêu đề email không được để trống' })
  @IsString({ message: 'Tiêu đề email phải là chuỗi' })
  subject: string;

  @IsNotEmpty({ message: 'Nội dung email không được để trống' })
  @IsString({ message: 'Nội dung email phải là chuỗi' })
  content: string;

  @IsOptional()
  @IsNumber({}, { message: 'ID segment phải là số' })
  segmentId?: number;

  @IsOptional()
  @IsArray({ message: 'Danh sách audience IDs phải là mảng' })
  @IsNumber({}, { each: true, message: 'Mỗi audience ID phải là số' })
  audienceIds?: number[];

  @IsOptional()
  @IsNumber({}, { message: 'Thời gian lên lịch phải là số (Unix timestamp)' })
  @Min(Date.now() / 1000, { message: 'Thời gian lên lịch phải trong tương lai' })
  scheduledAt?: number;

  @IsOptional()
  @ValidateNested()
  @Type(() => EmailServerConfigDto)
  server?: EmailServerConfigDto;
}
```

## 🔄 Worker Integration Rules

### 1. Queue Configuration
```typescript
// Module Registration
BullModule.registerQueue({
  name: QueueName.EMAIL_MARKETING,
})

// Job Names
export enum EmailMarketingJobName {
  SEND_EMAIL = 'send-email'
}

// Queue Options
const DEFAULT_JOB_OPTIONS = {
  attempts: 3,
  backoff: {
    type: 'exponential',
    delay: 2000
  },
  removeOnComplete: 100,
  removeOnFail: 50
};
```

### 2. Job Processing Rules
```typescript
// Job Data Structure
interface EmailMarketingJobDto {
  campaignId: number;
  audienceId: number;
  email: string;
  subject: string;
  content: string;
  customFields: Record<string, any>;
  server?: EmailServerConfig;
  trackingId: string;
  createdAt: number;
}

// Processing Logic (Worker Side)
@Processor(QueueName.EMAIL_MARKETING)
export class EmailMarketingProcessor {
  @Process(EmailMarketingJobName.SEND_EMAIL)
  async sendEmail(job: Job<EmailMarketingJobDto>) {
    const { data } = job;
    
    try {
      // 1. Replace placeholders
      const processedSubject = this.replacePlaceholders(data.subject, data.customFields);
      const processedContent = this.replacePlaceholders(data.content, data.customFields);
      
      // 2. Add tracking pixel
      const contentWithTracking = this.addTrackingPixel(processedContent, data.trackingId);
      
      // 3. Send email via SMTP
      const result = await this.emailService.sendEmail({
        to: data.email,
        subject: processedSubject,
        html: contentWithTracking,
        server: data.server
      });
      
      // 4. Update campaign history
      await this.updateCampaignHistory(data.campaignId, data.audienceId, SendStatus.SENT);
      
      return result;
    } catch (error) {
      // Update failed status
      await this.updateCampaignHistory(data.campaignId, data.audienceId, SendStatus.FAILED);
      throw error;
    }
  }
}
```

### 3. Tracking & Analytics
```typescript
// Tracking Pixel
private addTrackingPixel(content: string, trackingId: string): string {
  const pixelUrl = `${process.env.BASE_URL}/api/track/open/${trackingId}`;
  const trackingPixel = `<img src="${pixelUrl}" width="1" height="1" style="display:none;" />`;
  
  // Insert before closing body tag
  return content.replace('</body>', `${trackingPixel}</body>`);
}

// Click Tracking
private addClickTracking(content: string, trackingId: string): string {
  const baseUrl = process.env.BASE_URL;
  
  // Replace all links with tracking URLs
  return content.replace(
    /href="([^"]+)"/g,
    `href="${baseUrl}/api/track/click/${trackingId}?url=$1"`
  );
}

// Placeholder Replacement
private replacePlaceholders(content: string, customFields: Record<string, any>): string {
  let processedContent = content;
  
  // Replace custom fields
  Object.keys(customFields).forEach(key => {
    const placeholder = `{{${key}}}`;
    const value = customFields[key] || '';
    processedContent = processedContent.replace(new RegExp(placeholder, 'g'), value);
  });
  
  // Replace system placeholders
  processedContent = processedContent.replace(/{{currentDate}}/g, new Date().toLocaleDateString('vi-VN'));
  processedContent = processedContent.replace(/{{unsubscribeLink}}/g, `${process.env.BASE_URL}/unsubscribe`);
  
  return processedContent;
}
```

## 🔗 Email Server Integration Rules

### 1. SMTP Configuration Management

#### 1.1. Email Server Entity (Integration Module)
```typescript
// src/modules/integration/entities/email-server-configuration.entity.ts
@Entity('email_server_configurations')
export class EmailServerConfiguration {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'user_id' })
  userId: number;

  @Column({ name: 'server_name', length: 100 })
  serverName: string;

  @Column({ name: 'host', length: 255 })
  host: string;                    // smtp.gmail.com

  @Column({ name: 'port' })
  port: number;                    // 587, 465

  @Column({ name: 'username', length: 255 })
  username: string;                // Email account

  @Column({ name: 'password', length: 255 })
  password: string;                // Encrypted password

  @Column({ name: 'use_ssl' })
  useSsl: boolean;                 // SSL/TLS

  @Column({ name: 'use_start_tls', default: false })
  useStartTls: boolean;            // STARTTLS

  @Column({ name: 'is_active', default: true })
  isActive: boolean;

  @Column({ name: 'additional_settings', type: 'json', nullable: true })
  additionalSettings: any;

  @Column({ name: 'created_at', type: 'bigint' })
  createdAt: number;

  @Column({ name: 'updated_at', type: 'bigint' })
  updatedAt: number;
}
```

#### 1.2. SMTP Provider Support
```typescript
// Supported SMTP Providers
enum SMTPProvider {
  GMAIL = 'gmail',
  OUTLOOK = 'outlook',
  YAHOO = 'yahoo',
  MAILGUN = 'mailgun',
  SENDGRID = 'sendgrid',
  CUSTOM = 'custom'
}

// Provider Configurations
const SMTP_PROVIDERS = {
  [SMTPProvider.GMAIL]: {
    host: 'smtp.gmail.com',
    port: 587,
    secure: false,
    requiresAppPassword: true,
    documentation: 'https://support.google.com/accounts/answer/185833'
  },
  [SMTPProvider.OUTLOOK]: {
    host: 'smtp-mail.outlook.com',
    port: 587,
    secure: false,
    requiresAppPassword: false,
    documentation: 'https://support.microsoft.com/en-us/office/pop-imap-and-smtp-settings-8361e398-8af4-4e97-b147-6c6c4ac95353'
  },
  [SMTPProvider.YAHOO]: {
    host: 'smtp.mail.yahoo.com',
    port: 587,
    secure: false,
    requiresAppPassword: true,
    documentation: 'https://help.yahoo.com/kb/SLN4724.html'
  },
  [SMTPProvider.MAILGUN]: {
    host: 'smtp.mailgun.org',
    port: 587,
    secure: false,
    requiresAppPassword: false,
    documentation: 'https://documentation.mailgun.com/en/latest/user_manual.html#smtp'
  }
};
```

#### 1.3. Email Server Service Integration
```typescript
// EmailServerConfigurationUserService Integration
export class EmailMarketingService {
  constructor(
    private readonly emailServerService: EmailServerConfigurationUserService
  ) {}

  private async getEmailServerConfig(userId: number, serverId?: number): Promise<EmailServerConfiguration> {
    if (serverId) {
      // Use specific server configuration
      return await this.emailServerService.findOne(userId, serverId);
    } else {
      // Use default active server
      const servers = await this.emailServerService.findAll(userId);
      const activeServer = servers.find(s => s.isActive);

      if (!activeServer) {
        throw new BadRequestException('Không tìm thấy cấu hình email server nào');
      }

      return activeServer;
    }
  }

  // Test Email Server Connection
  async testEmailServerConnection(userId: number, serverId: number): Promise<boolean> {
    return await this.emailServerService.testConnection(userId, serverId);
  }
}
```

### 2. Email Delivery Rules

#### 2.1. Nodemailer Integration
```typescript
// Email Service với Nodemailer
export class EmailDeliveryService {
  async sendEmail(emailData: {
    to: string;
    subject: string;
    html: string;
    server: EmailServerConfiguration;
  }): Promise<any> {

    // Create transporter
    const transporter = nodemailer.createTransporter({
      host: emailData.server.host,
      port: emailData.server.port,
      secure: emailData.server.useSsl,
      requireTLS: emailData.server.useStartTls,
      auth: {
        user: emailData.server.username,
        pass: emailData.server.password // Decrypted password
      },
      ...emailData.server.additionalSettings
    });

    // Mail options
    const mailOptions = {
      from: `"${emailData.server.serverName}" <${emailData.server.username}>`,
      to: emailData.to,
      subject: emailData.subject,
      html: emailData.html
    };

    // Send email
    try {
      const result = await transporter.sendMail(mailOptions);

      return {
        success: true,
        messageId: result.messageId,
        response: result.response
      };
    } catch (error) {
      throw new AppException(
        ErrorCode.EMAIL_SENDING_ERROR,
        `Failed to send email: ${error.message}`
      );
    }
  }
}
```

#### 2.2. Rate Limiting & Throttling
```typescript
// Email Rate Limiting
interface EmailRateLimit {
  userId: number;
  emailsPerMinute: number;
  emailsPerHour: number;
  emailsPerDay: number;
  currentMinuteCount: number;
  currentHourCount: number;
  currentDayCount: number;
  lastResetMinute: number;
  lastResetHour: number;
  lastResetDay: number;
}

export class EmailRateLimitService {
  async checkRateLimit(userId: number): Promise<boolean> {
    const limit = await this.getRateLimit(userId);
    const now = Date.now();

    // Reset counters if needed
    this.resetCountersIfNeeded(limit, now);

    // Check limits
    if (limit.currentMinuteCount >= limit.emailsPerMinute) {
      throw new AppException(ErrorCode.RATE_LIMIT_EXCEEDED, 'Đã vượt quá giới hạn email/phút');
    }

    if (limit.currentHourCount >= limit.emailsPerHour) {
      throw new AppException(ErrorCode.RATE_LIMIT_EXCEEDED, 'Đã vượt quá giới hạn email/giờ');
    }

    if (limit.currentDayCount >= limit.emailsPerDay) {
      throw new AppException(ErrorCode.RATE_LIMIT_EXCEEDED, 'Đã vượt quá giới hạn email/ngày');
    }

    // Increment counters
    limit.currentMinuteCount++;
    limit.currentHourCount++;
    limit.currentDayCount++;

    await this.updateRateLimit(limit);
    return true;
  }
}
```

### 3. Email Tracking & Analytics

#### 3.1. Tracking Implementation
```typescript
// Tracking Controller
@Controller('api/track')
export class EmailTrackingController {

  @Get('open/:trackingId')
  async trackEmailOpen(@Param('trackingId') trackingId: string, @Res() res: Response) {
    try {
      // Parse tracking ID
      const { campaignId, audienceId } = this.parseTrackingId(trackingId);

      // Update campaign history
      await this.updateCampaignHistory(campaignId, audienceId, SendStatus.OPENED);

      // Return 1x1 transparent pixel
      const pixel = Buffer.from(
        'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
        'base64'
      );

      res.set({
        'Content-Type': 'image/png',
        'Content-Length': pixel.length,
        'Cache-Control': 'no-cache, no-store, must-revalidate'
      });

      res.send(pixel);
    } catch (error) {
      // Return empty pixel even on error
      res.status(200).send();
    }
  }

  @Get('click/:trackingId')
  async trackEmailClick(
    @Param('trackingId') trackingId: string,
    @Query('url') url: string,
    @Res() res: Response
  ) {
    try {
      // Parse tracking ID
      const { campaignId, audienceId } = this.parseTrackingId(trackingId);

      // Update campaign history
      await this.updateCampaignHistory(campaignId, audienceId, SendStatus.CLICKED);

      // Redirect to original URL
      res.redirect(decodeURIComponent(url));
    } catch (error) {
      // Redirect to original URL even on error
      res.redirect(decodeURIComponent(url));
    }
  }

  private parseTrackingId(trackingId: string): { campaignId: number; audienceId: number } {
    // Format: campaignId_audienceId_timestamp
    const parts = trackingId.split('_');
    return {
      campaignId: parseInt(parts[0]),
      audienceId: parseInt(parts[1])
    };
  }
}
```

#### 3.2. Analytics Calculation
```typescript
// Email Analytics Service
export class EmailAnalyticsService {
  async getCampaignMetrics(campaignId: number): Promise<EmailCampaignMetrics> {
    const history = await this.campaignHistoryRepository.find({
      where: { campaignId }
    });

    // Calculate basic metrics
    const totalSent = history.filter(h => h.status === SendStatus.SENT).length;
    const totalDelivered = history.filter(h =>
      [SendStatus.DELIVERED, SendStatus.OPENED, SendStatus.CLICKED].includes(h.status)
    ).length;
    const totalOpened = history.filter(h =>
      [SendStatus.OPENED, SendStatus.CLICKED].includes(h.status)
    ).length;
    const totalClicked = history.filter(h => h.status === SendStatus.CLICKED).length;
    const totalBounced = history.filter(h => h.status === SendStatus.BOUNCED).length;

    // Calculate rates
    const deliveryRate = totalSent > 0 ? (totalDelivered / totalSent) * 100 : 0;
    const openRate = totalDelivered > 0 ? (totalOpened / totalDelivered) * 100 : 0;
    const clickRate = totalOpened > 0 ? (totalClicked / totalOpened) * 100 : 0;
    const bounceRate = totalSent > 0 ? (totalBounced / totalSent) * 100 : 0;

    return {
      campaignId,
      totalSent,
      totalDelivered,
      totalOpened,
      totalClicked,
      totalBounced,
      deliveryRate: Math.round(deliveryRate * 10) / 10,
      openRate: Math.round(openRate * 10) / 10,
      clickRate: Math.round(clickRate * 10) / 10,
      bounceRate: Math.round(bounceRate * 10) / 10
    };
  }

  // Real-time Analytics
  async getRealTimeMetrics(campaignId: number): Promise<RealTimeMetrics> {
    const now = Date.now();
    const oneHourAgo = now - (60 * 60 * 1000);

    const recentHistory = await this.campaignHistoryRepository.find({
      where: {
        campaignId,
        sentAt: MoreThanOrEqual(oneHourAgo)
      },
      order: { sentAt: 'DESC' }
    });

    // Group by time intervals (15-minute buckets)
    const timeIntervals = this.groupByTimeIntervals(recentHistory, 15);

    return {
      campaignId,
      timeRange: { start: oneHourAgo, end: now },
      intervals: timeIntervals,
      totalEvents: recentHistory.length
    };
  }
}
```

## 🔒 Security & Compliance Rules

### 1. Data Protection
```typescript
// Email Content Sanitization
export class EmailSecurityService {
  sanitizeEmailContent(content: string): string {
    // Remove potentially dangerous HTML
    const cleanContent = DOMPurify.sanitize(content, {
      ALLOWED_TAGS: ['p', 'br', 'strong', 'em', 'u', 'a', 'img', 'div', 'span', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6'],
      ALLOWED_ATTR: ['href', 'src', 'alt', 'style', 'class', 'id'],
      ALLOW_DATA_ATTR: false
    });

    return cleanContent;
  }

  // Validate email addresses
  validateEmailAddress(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  // Check for spam indicators
  checkSpamIndicators(subject: string, content: string): SpamCheckResult {
    const spamKeywords = ['free', 'urgent', 'limited time', 'act now', 'click here'];
    const suspiciousPatterns = [
      /\$\d+/g,                    // Money amounts
      /[A-Z]{5,}/g,                // Excessive caps
      /!{3,}/g,                    // Multiple exclamation marks
      /\b(viagra|casino|lottery)\b/gi // Spam words
    ];

    let spamScore = 0;
    const issues = [];

    // Check spam keywords
    spamKeywords.forEach(keyword => {
      if (subject.toLowerCase().includes(keyword) || content.toLowerCase().includes(keyword)) {
        spamScore += 10;
        issues.push(`Contains spam keyword: ${keyword}`);
      }
    });

    // Check suspicious patterns
    suspiciousPatterns.forEach(pattern => {
      if (pattern.test(content)) {
        spamScore += 5;
        issues.push(`Contains suspicious pattern: ${pattern.source}`);
      }
    });

    return {
      spamScore,
      isSpam: spamScore > 50,
      issues,
      recommendation: spamScore > 30 ? 'Review content for spam indicators' : 'Content looks good'
    };
  }
}
```

### 2. GDPR Compliance
```typescript
// GDPR Compliance Service
export class EmailGDPRService {
  // Consent tracking
  async trackEmailConsent(audienceId: number, consentData: {
    consentGiven: boolean;
    consentDate: number;
    consentSource: string;
    ipAddress: string;
    userAgent: string;
  }): Promise<void> {
    await this.consentRepository.save({
      audienceId,
      consentType: 'email_marketing',
      ...consentData
    });
  }

  // Unsubscribe handling
  async processUnsubscribe(trackingId: string): Promise<void> {
    const { campaignId, audienceId } = this.parseTrackingId(trackingId);

    // Update campaign history
    await this.updateCampaignHistory(campaignId, audienceId, SendStatus.UNSUBSCRIBED);

    // Add to suppression list
    await this.addToSuppressionList(audienceId);

    // Log unsubscribe event
    await this.logGDPREvent({
      audienceId,
      eventType: 'unsubscribe',
      timestamp: Date.now(),
      source: 'email_link'
    });
  }

  // Data export (Right to Access)
  async exportUserData(userId: number): Promise<UserDataExport> {
    const templates = await this.templateRepository.find({ where: { userId } });
    const campaigns = await this.campaignRepository.find({ where: { userId } });
    const audiences = await this.audienceRepository.find({ where: { userId } });

    return {
      userId,
      exportDate: Date.now(),
      data: {
        templates: templates.map(t => this.sanitizeTemplate(t)),
        campaigns: campaigns.map(c => this.sanitizeCampaign(c)),
        audiences: audiences.map(a => this.sanitizeAudience(a))
      }
    };
  }

  // Data deletion (Right to Erasure)
  async deleteUserData(userId: number): Promise<void> {
    // Soft delete to maintain referential integrity
    await this.templateRepository.update({ userId }, { status: 'DELETED' });
    await this.campaignRepository.update({ userId }, { status: 'DELETED' });
    await this.audienceRepository.update({ userId }, { email: 'DELETED', phone: 'DELETED' });

    // Log deletion event
    await this.logGDPREvent({
      userId,
      eventType: 'data_deletion',
      timestamp: Date.now(),
      source: 'user_request'
    });
  }
}
```

## 📈 Performance Optimization Rules

### 1. Database Optimization
```typescript
// Required Indexes
CREATE INDEX idx_user_template_email_user_id ON user_template_email(user_id);
CREATE INDEX idx_user_template_email_status ON user_template_email(status);
CREATE INDEX idx_user_template_email_created_at ON user_template_email(created_at);

CREATE INDEX idx_user_campaigns_user_id ON user_campaigns(user_id);
CREATE INDEX idx_user_campaigns_status ON user_campaigns(status);
CREATE INDEX idx_user_campaigns_platform ON user_campaigns(platform);
CREATE INDEX idx_user_campaigns_scheduled_at ON user_campaigns(scheduled_at);

CREATE INDEX idx_user_campaign_history_campaign_id ON user_campaign_history(campaign_id);
CREATE INDEX idx_user_campaign_history_audience_id ON user_campaign_history(audience_id);
CREATE INDEX idx_user_campaign_history_status ON user_campaign_history(status);
CREATE INDEX idx_user_campaign_history_sent_at ON user_campaign_history(sent_at);

// Composite Indexes
CREATE INDEX idx_user_campaigns_user_platform_status ON user_campaigns(user_id, platform, status);
CREATE INDEX idx_user_campaign_history_campaign_status ON user_campaign_history(campaign_id, status);
```

### 2. Query Optimization
```typescript
// Optimized Repository Methods
export class UserTemplateEmailRepository {
  async findWithPagination(userId: number, query: TemplateEmailQueryDto): Promise<PaginatedResult<UserTemplateEmail>> {
    const queryBuilder = this.createQueryBuilder('template')
      .where('template.userId = :userId', { userId })
      .andWhere('template.status != :deletedStatus', { deletedStatus: 'DELETED' });

    // Apply filters
    if (query.name) {
      queryBuilder.andWhere('template.name ILIKE :name', { name: `%${query.name}%` });
    }

    if (query.status) {
      queryBuilder.andWhere('template.status = :status', { status: query.status });
    }

    if (query.type) {
      queryBuilder.andWhere('template.type = :type', { type: query.type });
    }

    // Apply sorting
    const sortBy = query.sortBy || 'createdAt';
    const sortDirection = query.sortDirection || 'DESC';
    queryBuilder.orderBy(`template.${sortBy}`, sortDirection);

    // Apply pagination
    const page = query.page || 1;
    const limit = query.limit || 10;
    const offset = (page - 1) * limit;

    queryBuilder.skip(offset).take(limit);

    // Execute query
    const [data, total] = await queryBuilder.getManyAndCount();

    return {
      data,
      meta: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    };
  }
}
```

### 3. Caching Strategy
```typescript
// Redis Caching for Templates
export class TemplateEmailCacheService {
  private readonly CACHE_TTL = 3600; // 1 hour
  private readonly CACHE_PREFIX = 'template_email:';

  async getTemplate(templateId: number): Promise<UserTemplateEmail | null> {
    const cacheKey = `${this.CACHE_PREFIX}${templateId}`;

    // Try cache first
    const cached = await this.redisService.get(cacheKey);
    if (cached) {
      return JSON.parse(cached);
    }

    // Fetch from database
    const template = await this.templateRepository.findOne({ where: { id: templateId } });
    if (template) {
      // Cache the result
      await this.redisService.setex(cacheKey, this.CACHE_TTL, JSON.stringify(template));
    }

    return template;
  }

  async invalidateTemplate(templateId: number): Promise<void> {
    const cacheKey = `${this.CACHE_PREFIX}${templateId}`;
    await this.redisService.del(cacheKey);
  }

  async invalidateUserTemplates(userId: number): Promise<void> {
    const pattern = `${this.CACHE_PREFIX}user:${userId}:*`;
    const keys = await this.redisService.keys(pattern);

    if (keys.length > 0) {
      await this.redisService.del(...keys);
    }
  }
}
```

## ✅ Testing Rules

### 1. Unit Testing
```typescript
// Template Service Tests
describe('UserTemplateEmailService', () => {
  let service: UserTemplateEmailService;
  let repository: UserTemplateEmailRepository;

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      providers: [
        UserTemplateEmailService,
        {
          provide: UserTemplateEmailRepository,
          useValue: {
            findByUserIdAndName: jest.fn(),
            save: jest.fn(),
            findWithPagination: jest.fn()
          }
        }
      ]
    }).compile();

    service = module.get<UserTemplateEmailService>(UserTemplateEmailService);
    repository = module.get<UserTemplateEmailRepository>(UserTemplateEmailRepository);
  });

  describe('create', () => {
    it('should create template successfully', async () => {
      const userId = 1;
      const createDto: CreateTemplateEmailDto = {
        name: 'Test Template',
        subject: 'Test Subject',
        htmlContent: '<p>Hello {{firstName}}</p>',
        type: TemplateType.NEWSLETTER
      };

      repository.findByUserIdAndName = jest.fn().mockResolvedValue(null);
      repository.save = jest.fn().mockResolvedValue({ id: 1, ...createDto });

      const result = await service.create(userId, createDto);

      expect(result).toBeDefined();
      expect(result.name).toBe(createDto.name);
      expect(repository.save).toHaveBeenCalled();
    });

    it('should throw error for duplicate name', async () => {
      const userId = 1;
      const createDto: CreateTemplateEmailDto = {
        name: 'Existing Template',
        subject: 'Test Subject',
        htmlContent: '<p>Content</p>'
      };

      repository.findByUserIdAndName = jest.fn().mockResolvedValue({ id: 1 });

      await expect(service.create(userId, createDto)).rejects.toThrow(ConflictException);
    });
  });
});
```

### 2. Integration Testing
```typescript
// Email Campaign Integration Tests
describe('EmailCampaignController (e2e)', () => {
  let app: INestApplication;
  let authToken: string;

  beforeAll(async () => {
    const moduleFixture = await Test.createTestingModule({
      imports: [AppModule]
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    // Get auth token
    authToken = await getAuthToken(app);
  });

  describe('POST /v1/user/marketing/email-campaigns', () => {
    it('should create email campaign successfully', async () => {
      const createDto = {
        title: 'Test Campaign',
        subject: 'Test Subject',
        content: '<p>Test Content</p>',
        audienceIds: [1, 2, 3]
      };

      const response = await request(app.getHttpServer())
        .post('/v1/user/marketing/email-campaigns')
        .set('Authorization', `Bearer ${authToken}`)
        .send(createDto)
        .expect(201);

      expect(response.body.data.campaignId).toBeDefined();
      expect(response.body.data.jobCount).toBe(3);
    });

    it('should validate required fields', async () => {
      const invalidDto = {
        title: 'Test Campaign'
        // Missing required fields
      };

      await request(app.getHttpServer())
        .post('/v1/user/marketing/email-campaigns')
        .set('Authorization', `Bearer ${authToken}`)
        .send(invalidDto)
        .expect(400);
    });
  });
});
```

## 🎯 Kết Luận

Tài liệu này cung cấp rules đầy đủ cho email marketing user bao gồm:

### ✅ **Đã Triển Khai**
- **Entity Layer**: UserTemplateEmail, UserCampaign, UserCampaignHistory
- **Service Layer**: Template management, Campaign creation, Analytics
- **Controller Layer**: RESTful APIs với Swagger documentation
- **Worker Integration**: BullMQ queue processing
- **Email Server**: SMTP configuration và integration

### 🔧 **Tính Năng Chính**
- **Template Management**: Tạo, sửa, xóa template với placeholder system
- **Campaign Creation**: Tạo campaign với audience/segment targeting
- **Queue Processing**: Background email sending với retry mechanism
- **Tracking & Analytics**: Open/click tracking, campaign metrics
- **SMTP Integration**: Multi-provider SMTP support

### 🛡️ **Security & Performance**
- **Data Validation**: Comprehensive input validation
- **Rate Limiting**: Email sending limits
- **GDPR Compliance**: Consent tracking, unsubscribe handling
- **Database Optimization**: Proper indexing và query optimization
- **Caching**: Redis caching for performance

### 📊 **Monitoring & Analytics**
- **Real-time Metrics**: Campaign performance tracking
- **Email Events**: Open, click, bounce tracking
- **User Analytics**: Engagement scoring
- **Error Handling**: Comprehensive error tracking và reporting

Hệ thống được thiết kế để scalable, maintainable và tuân thủ các best practices của NestJS và TypeORM.
