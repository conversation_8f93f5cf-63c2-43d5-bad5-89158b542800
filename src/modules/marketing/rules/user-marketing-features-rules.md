# 📋 User Marketing Features - Development Rules

> Quy tắc phát triển chi tiết cho các tính năng marketing dành cho user dựa trên cấu trúc hiện tại

## 🏗️ Cấu Trúc Hiện Tại

### 1. User Marketing Module Structure
```
src/modules/marketing/user/
├── controllers/           # API Controllers
│   ├── user-tag.controller.ts
│   ├── user-audience.controller.ts
│   ├── user-segment.controller.ts
│   ├── user-campaign.controller.ts
│   ├── user-template-email.controller.ts
│   ├── email-campaign.controller.ts
│   ├── marketing-overview.controller.ts
│   └── zalo-*.controller.ts (Zalo integration)
├── services/             # Business Logic
├── entities/             # Database Models
├── dto/                  # Data Transfer Objects
├── repositories/         # Data Access Layer
└── migrations/           # Database Migrations
```

### 2. Current API Endpoints
```
Base URL: /v1/user/marketing/

Core Features:
- GET/POST/PUT/DELETE /tags
- GET/POST/PUT/DELETE /audiences  
- GET/POST/PUT/DELETE /segments
- GET/POST/PUT/DELETE /campaigns
- GET/POST/PUT/DELETE /templates

Email Marketing:
- POST /email-campaigns
- GET /email-campaigns
- GET /email-campaigns/recent

Analytics:
- GET /overview
- GET /overview/recent-templates
- GET /statistics

Zalo Integration:
- GET/POST /zalo/*
```

## 🎯 Core User Features Rules

### 1. Tag Management (Quản lý Nhãn)

#### 1.1. Business Rules
```typescript
// Tag entity constraints
- name: required, max 255 chars, unique per user
- color: optional, default '#3498db', hex format
- userId: required, auto-set from JWT
- createdAt/updatedAt: auto-managed

// Validation rules
@IsNotEmpty({ message: 'Tên tag không được để trống' })
@IsString({ message: 'Tên tag phải là chuỗi' })
@MaxLength(255, { message: 'Tên tag không được vượt quá 255 ký tự' })
name: string;

@IsOptional()
@IsHexColor({ message: 'Màu sắc phải là mã hex hợp lệ' })
color?: string;
```

#### 1.2. API Endpoints
```typescript
// GET /marketing/tags - Lấy danh sách tag
async findAll(@CurrentUser() user: JwtPayload): Promise<AppApiResponse<TagResponseDto[]>>

// POST /marketing/tags - Tạo tag mới  
async create(@CurrentUser() user: JwtPayload, @Body() createTagDto: CreateTagDto)

// PUT /marketing/tags/:id - Cập nhật tag
async update(@CurrentUser() user: JwtPayload, @Param('id') id: string, @Body() updateTagDto: UpdateTagDto)

// DELETE /marketing/tags/:id - Xóa tag
async remove(@CurrentUser() user: JwtPayload, @Param('id') id: string)
```

#### 1.3. Security Rules
- **User Isolation**: Luôn filter theo `userId` từ JWT
- **Ownership Validation**: Kiểm tra tag thuộc về user trước khi thao tác
- **No Cross-User Access**: User chỉ xem/sửa tag của mình

### 2. Audience Management (Quản lý Đối tượng)

#### 2.1. Business Rules
```typescript
// Audience entity constraints
- email: required, valid email format, unique per user
- phone: optional, valid phone format
- userId: required, auto-set from JWT
- customFields: optional, JSONB array
- tagIds: optional, array of tag IDs

// Validation rules
@IsNotEmpty({ message: 'Email không được để trống' })
@IsEmail({}, { message: 'Email không hợp lệ' })
email: string;

@IsOptional()
@IsPhoneNumber(undefined, { message: 'Số điện thoại không hợp lệ' })
phone?: string;

@IsOptional()
@ValidateNested({ each: true })
@Type(() => CreateCustomFieldDto)
customFields?: CreateCustomFieldDto[];
```

#### 2.2. Advanced Features
```typescript
// Bulk operations
- POST /audiences/bulk - Import từ CSV/Excel
- DELETE /audiences/bulk - Xóa nhiều audience
- PUT /audiences/bulk - Cập nhật hàng loạt

// Search & Filter
- GET /audiences?email=xxx&phone=xxx&tagId=xxx
- GET /audiences?customFieldName=xxx&customFieldValue=xxx
- Pagination: page, limit, sortBy, sortDirection

// Custom Fields Support
- Dynamic custom fields per audience
- Validation theo field type (string, number, date, boolean)
- Search theo custom field values
```

#### 2.3. Integration Rules
```typescript
// Import/Export
- Support CSV, Excel formats
- Validate data before import
- Error reporting cho failed imports
- Background processing cho large datasets

// API Integration
- Webhook support cho external systems
- REST API cho third-party integrations
- Rate limiting để tránh spam
```

### 3. Segment Management (Quản lý Phân đoạn)

#### 3.1. Business Rules
```typescript
// Segment criteria structure
interface SegmentCriteria {
  operator: 'AND' | 'OR';
  conditions: SegmentCondition[];
}

interface SegmentCondition {
  field: string;           // 'email', 'phone', 'customField.name'
  operator: string;        // 'equals', 'contains', 'startsWith', 'endsWith', 'gt', 'lt'
  value: any;             // Giá trị so sánh
  type: 'string' | 'number' | 'date' | 'boolean';
}
```

#### 3.2. Dynamic Segmentation
```typescript
// Real-time audience calculation
async getAudiencesInSegment(userId: number, segment: UserSegment): Promise<UserAudience[]> {
  // 1. Lấy tất cả audience của user
  // 2. Apply segment criteria
  // 3. Return filtered results
}

// Segment preview
async previewSegment(userId: number, criteria: SegmentCriteria): Promise<{
  count: number;
  sampleAudiences: UserAudience[];
}> {
  // Preview trước khi save segment
}
```

#### 3.3. Performance Rules
```typescript
// Caching strategy
- Cache segment results for 5 minutes
- Invalidate cache khi audience data changes
- Use Redis for caching

// Query optimization
- Index trên userId, email, phone
- Limit segment preview to 100 samples
- Background calculation cho large segments
```

### 4. Campaign Management (Quản lý Chiến dịch)

#### 4.1. Campaign Types
```typescript
enum CampaignPlatform {
  EMAIL = 'email',
  SMS = 'sms', 
  ZALO = 'zalo',
  PUSH = 'push'
}

enum CampaignStatus {
  DRAFT = 'draft',
  SCHEDULED = 'scheduled', 
  SENDING = 'sending',
  SENT = 'sent',
  PAUSED = 'paused',
  CANCELLED = 'cancelled',
  COMPLETED = 'completed'
}
```

#### 4.2. Campaign Execution Rules
```typescript
// Scheduling
- Support immediate send hoặc scheduled send
- Timezone handling cho scheduled campaigns
- Automatic status updates

// Audience Selection
- Support segment-based hoặc manual audience selection
- Validate audience exists và belongs to user
- Prevent duplicate sends to same audience

// Content Management
- Template-based content hoặc custom content
- Placeholder replacement cho personalization
- Content validation trước khi send
```

#### 4.3. Queue Processing
```typescript
// Email campaigns
- Use BullMQ cho background processing
- Batch processing để tránh rate limits
- Retry mechanism cho failed sends
- Progress tracking

// Job structure
interface EmailMarketingJobDto {
  campaignId: number;
  audienceId: number;
  email: string;
  subject: string;
  content: string;
  customFields: any[];
  server: any;
  trackingId: string;
  createdAt: number;
}
```

### 5. Template Management (Quản lý Mẫu)

#### 5.1. Email Templates
```typescript
// Template structure
interface EmailTemplate {
  id: number;
  userId: number;
  name: string;
  subject: string;
  content: string;        // HTML content
  placeholders: string[]; // Available placeholders
  category?: string;
  status: 'active' | 'inactive' | 'draft';
  createdAt: number;
  updatedAt: number;
}
```

#### 5.2. Template Features
```typescript
// Placeholder system
- {{firstName}}, {{lastName}}, {{email}}, {{phone}}
- Custom field placeholders: {{customField.fieldName}}
- System placeholders: {{unsubscribeLink}}, {{trackingPixel}}

// Template validation
- HTML validation
- Placeholder validation
- Preview functionality
- Test send capability
```

#### 5.3. Template Categories
```typescript
// Pre-defined categories
enum TemplateCategory {
  WELCOME = 'welcome',
  PROMOTIONAL = 'promotional', 
  NEWSLETTER = 'newsletter',
  TRANSACTIONAL = 'transactional',
  FOLLOW_UP = 'follow_up',
  CUSTOM = 'custom'
}
```

## 🚀 Advanced Features Rules

### 1. Email Marketing Automation

#### 1.1. Drip Campaigns
```typescript
// Multi-step email sequences
interface DripCampaign {
  id: number;
  name: string;
  steps: DripStep[];
  triggerType: 'signup' | 'purchase' | 'custom';
  isActive: boolean;
}

interface DripStep {
  stepNumber: number;
  templateId: number;
  delayDays: number;
  conditions?: SegmentCriteria;
}
```

#### 1.2. Behavioral Triggers
```typescript
// Event-based automation
enum TriggerEvent {
  USER_SIGNUP = 'user_signup',
  EMAIL_OPENED = 'email_opened', 
  LINK_CLICKED = 'link_clicked',
  PURCHASE_MADE = 'purchase_made',
  CART_ABANDONED = 'cart_abandoned'
}

// Automation rules
interface AutomationRule {
  id: number;
  name: string;
  triggerEvent: TriggerEvent;
  conditions: SegmentCriteria;
  actions: AutomationAction[];
  isActive: boolean;
}
```

### 2. Analytics & Reporting

#### 2.1. Campaign Analytics
```typescript
// Email metrics
interface EmailCampaignStats {
  campaignId: number;
  sentCount: number;
  deliveredCount: number;
  openCount: number;
  clickCount: number;
  bounceCount: number;
  unsubscribeCount: number;
  
  // Calculated metrics
  deliveryRate: number;    // (delivered/sent) * 100
  openRate: number;        // (opened/delivered) * 100  
  clickRate: number;       // (clicked/opened) * 100
  bounceRate: number;      // (bounced/sent) * 100
  unsubscribeRate: number; // (unsubscribed/delivered) * 100
}
```

#### 2.2. Real-time Tracking
```typescript
// Tracking events
enum EmailEvent {
  SENT = 'sent',
  DELIVERED = 'delivered',
  OPENED = 'opened', 
  CLICKED = 'clicked',
  BOUNCED = 'bounced',
  UNSUBSCRIBED = 'unsubscribed',
  SPAM_REPORTED = 'spam_reported'
}

// Event logging
interface EmailEventLog {
  id: number;
  campaignId: number;
  audienceId: number;
  event: EmailEvent;
  timestamp: number;
  metadata?: any;
}
```

### 3. Integration Features

#### 3.1. Zalo Integration (Hiện có)
```typescript
// Zalo features đã implement
- Zalo Official Account management
- ZNS (Zalo Notification Service) templates
- Zalo message sending
- Zalo follower management
- Zalo automation campaigns
- Zalo segment targeting
```

#### 3.2. Future Integrations
```typescript
// SMS Integration
interface SmsProvider {
  type: 'twilio' | 'nexmo' | 'infobip';
  apiKey: string;
  senderId: string;
  isActive: boolean;
}

// Social Media Integration
interface SocialMediaAccount {
  platform: 'facebook' | 'instagram' | 'twitter';
  accountId: string;
  accessToken: string;
  isActive: boolean;
}
```

## 📊 Performance & Scalability Rules

### 1. Database Optimization
```typescript
// Required indexes
- user_campaigns: (userId, status, platform, createdAt)
- user_audience: (userId, email, phone, createdAt)
- user_segments: (userId, createdAt)
- user_campaign_history: (campaignId, audienceId, status)

// Query patterns
- Always filter by userId first
- Use pagination for large datasets
- Implement query result caching
- Use database connection pooling
```

### 2. Queue Management
```typescript
// Queue configuration
- Separate queues cho different platforms
- Priority queues cho urgent campaigns
- Dead letter queues cho failed jobs
- Rate limiting per user/platform

// Monitoring
- Job success/failure rates
- Queue length monitoring
- Processing time metrics
- Error alerting
```

### 3. Caching Strategy
```typescript
// Cache layers
- Redis cho session data
- Application cache cho template data
- Database query result cache
- CDN cho static assets

// Cache invalidation
- Time-based expiration
- Event-based invalidation
- Manual cache clearing
- Cache warming strategies
```

## 🔒 Security & Compliance Rules

### 1. Data Protection
```typescript
// GDPR Compliance
- User consent management
- Data export functionality
- Data deletion (right to be forgotten)
- Audit logging

// Email compliance
- Unsubscribe links in all emails
- Bounce handling
- Spam complaint handling
- Sender reputation management
```

### 2. Rate Limiting
```typescript
// API rate limits
- 1000 requests/hour per user
- 100 campaigns/day per user
- 10,000 emails/day per user (configurable)
- Burst protection

// Email sending limits
- Respect ESP rate limits
- Implement backoff strategies
- Monitor sender reputation
- Automatic throttling
```

### 3. Input Validation
```typescript
// Sanitization rules
- HTML content sanitization
- Email address validation
- Phone number validation
- File upload validation

// Business logic validation
- Audience ownership validation
- Template usage validation
- Campaign scheduling validation
- Resource quota validation
```

## ✅ Development Checklist

### Before Adding New Features:
- [ ] Check existing entities can be extended
- [ ] Follow user isolation patterns
- [ ] Implement proper validation
- [ ] Add comprehensive error handling
- [ ] Include audit logging
- [ ] Write unit tests
- [ ] Update API documentation
- [ ] Consider performance impact
- [ ] Plan migration strategy
- [ ] Review security implications

### Code Quality Standards:
- [ ] Use TypeScript strict mode
- [ ] Follow naming conventions
- [ ] Implement proper error codes
- [ ] Add comprehensive logging
- [ ] Use transactions for data consistency
- [ ] Implement proper pagination
- [ ] Add input sanitization
- [ ] Include rate limiting
- [ ] Write integration tests
- [ ] Document API endpoints

## 🎨 User Experience Features Rules

### 1. Dashboard & Overview

#### 1.1. Marketing Overview API (Đã implement)
```typescript
// Current endpoints
GET /marketing/overview
- totalTemplates: number
- openRate: number (%)
- clickRate: number (%)
- totalEmailsSent: number

GET /marketing/overview/recent-templates
- templates: RecentTemplateDto[]
- pagination support
```

#### 1.2. Enhanced Dashboard Features
```typescript
// Additional metrics cần implement
interface MarketingDashboard {
  // Campaign metrics
  totalCampaigns: number;
  activeCampaigns: number;
  scheduledCampaigns: number;

  // Audience metrics
  totalAudiences: number;
  activeAudiences: number;
  segmentCount: number;

  // Performance metrics
  avgOpenRate: number;
  avgClickRate: number;
  topPerformingCampaign: CampaignSummary;

  // Recent activity
  recentCampaigns: CampaignSummary[];
  recentAudiences: AudienceSummary[];
}
```

#### 1.3. Real-time Updates
```typescript
// WebSocket integration cho real-time updates
interface RealTimeUpdate {
  type: 'campaign_status' | 'email_opened' | 'email_clicked';
  campaignId: number;
  data: any;
  timestamp: number;
}

// Server-Sent Events cho live metrics
GET /marketing/overview/live-metrics
- Stream real-time campaign performance
- Update dashboard without refresh
```

### 2. Campaign Builder Features

#### 2.1. Visual Campaign Builder
```typescript
// Drag & drop campaign builder
interface CampaignStep {
  id: string;
  type: 'trigger' | 'condition' | 'action' | 'delay';
  config: any;
  position: { x: number; y: number };
  connections: string[]; // Connected step IDs
}

interface VisualCampaign {
  id: number;
  name: string;
  steps: CampaignStep[];
  isActive: boolean;
}
```

#### 2.2. A/B Testing
```typescript
// Split testing cho campaigns
interface ABTest {
  id: number;
  campaignId: number;
  name: string;
  variants: ABTestVariant[];
  trafficSplit: number[]; // [50, 50] for 50/50 split
  winnerCriteria: 'open_rate' | 'click_rate' | 'conversion_rate';
  status: 'running' | 'completed' | 'paused';
}

interface ABTestVariant {
  id: string;
  name: string;
  templateId: number;
  subject: string;
  metrics: {
    sent: number;
    opened: number;
    clicked: number;
  };
}
```

#### 2.3. Campaign Templates
```typescript
// Pre-built campaign templates
enum CampaignTemplate {
  WELCOME_SERIES = 'welcome_series',
  PRODUCT_LAUNCH = 'product_launch',
  ABANDONED_CART = 'abandoned_cart',
  RE_ENGAGEMENT = 're_engagement',
  BIRTHDAY_CAMPAIGN = 'birthday_campaign',
  SEASONAL_PROMOTION = 'seasonal_promotion'
}

interface CampaignTemplateConfig {
  template: CampaignTemplate;
  steps: CampaignStep[];
  defaultSettings: any;
  customizableFields: string[];
}
```

### 3. Advanced Segmentation

#### 3.1. Behavioral Segmentation
```typescript
// Segment dựa trên hành vi
interface BehavioralCriteria {
  emailEngagement: {
    openedInLast: number; // days
    clickedInLast: number; // days
    unopenedInLast: number; // days
  };

  campaignInteraction: {
    respondedToCampaigns: number[];
    ignoredCampaigns: number[];
  };

  websiteActivity: {
    visitedPages: string[];
    timeOnSite: number; // minutes
    lastVisit: number; // timestamp
  };
}
```

#### 3.2. Dynamic Segments
```typescript
// Auto-updating segments
interface DynamicSegment extends UserSegment {
  isAutoUpdate: boolean;
  updateFrequency: 'hourly' | 'daily' | 'weekly';
  lastUpdated: number;
  audienceCount: number;

  // Segment growth tracking
  growthHistory: {
    date: string;
    count: number;
  }[];
}
```

#### 3.3. Segment Performance
```typescript
// Segment analytics
interface SegmentPerformance {
  segmentId: number;
  totalCampaigns: number;
  avgOpenRate: number;
  avgClickRate: number;
  totalRevenue?: number;
  bestPerformingCampaign: CampaignSummary;
  engagementTrend: {
    date: string;
    openRate: number;
    clickRate: number;
  }[];
}
```

### 4. Email Design & Content

#### 4.1. Visual Email Builder
```typescript
// Drag & drop email builder
interface EmailBlock {
  id: string;
  type: 'text' | 'image' | 'button' | 'divider' | 'social' | 'product';
  config: any;
  style: CSSProperties;
  position: number;
}

interface EmailDesign {
  id: number;
  templateId: number;
  blocks: EmailBlock[];
  globalStyles: {
    backgroundColor: string;
    fontFamily: string;
    primaryColor: string;
    secondaryColor: string;
  };
}
```

#### 4.2. Content Personalization
```typescript
// Advanced personalization
interface PersonalizationRule {
  id: number;
  name: string;
  condition: SegmentCriteria;
  content: {
    subject?: string;
    blocks?: EmailBlock[];
    images?: string[];
  };
  priority: number;
}

// Dynamic content blocks
interface DynamicContent {
  blockId: string;
  rules: PersonalizationRule[];
  defaultContent: any;
}
```

#### 4.3. Content Library
```typescript
// Reusable content components
interface ContentAsset {
  id: number;
  type: 'image' | 'text_block' | 'button' | 'template_section';
  name: string;
  content: any;
  tags: string[];
  usageCount: number;
  createdAt: number;
}

// Content organization
interface ContentFolder {
  id: number;
  name: string;
  parentId?: number;
  assets: ContentAsset[];
  subfolders: ContentFolder[];
}
```

### 5. Automation Workflows

#### 5.1. Trigger-based Automation
```typescript
// Event triggers
interface AutomationTrigger {
  type: 'api_event' | 'email_event' | 'time_based' | 'user_action';
  config: {
    eventName?: string;
    conditions?: SegmentCriteria;
    schedule?: CronExpression;
    delay?: number; // minutes
  };
}

// Workflow actions
interface AutomationAction {
  type: 'send_email' | 'add_tag' | 'remove_tag' | 'update_field' | 'wait' | 'webhook';
  config: any;
  conditions?: SegmentCriteria;
}
```

#### 5.2. Multi-channel Workflows
```typescript
// Cross-platform automation
interface MultiChannelWorkflow {
  id: number;
  name: string;
  channels: ('email' | 'sms' | 'zalo' | 'push')[];
  steps: WorkflowStep[];
  isActive: boolean;
}

interface WorkflowStep {
  id: string;
  channel: string;
  action: AutomationAction;
  delay?: number;
  conditions?: SegmentCriteria;
  nextSteps: string[];
}
```

### 6. Analytics & Reporting

#### 6.1. Advanced Analytics
```typescript
// Comprehensive reporting
interface AnalyticsReport {
  dateRange: { start: number; end: number };
  campaigns: CampaignAnalytics[];
  segments: SegmentAnalytics[];
  overall: {
    totalSent: number;
    totalOpened: number;
    totalClicked: number;
    revenue?: number;
    roi?: number;
  };
  trends: {
    date: string;
    sent: number;
    opened: number;
    clicked: number;
  }[];
}
```

#### 6.2. Custom Reports
```typescript
// User-defined reports
interface CustomReport {
  id: number;
  name: string;
  metrics: string[];
  filters: ReportFilter[];
  groupBy: string[];
  dateRange: 'last_7_days' | 'last_30_days' | 'custom';
  schedule?: {
    frequency: 'daily' | 'weekly' | 'monthly';
    recipients: string[];
  };
}

interface ReportFilter {
  field: string;
  operator: string;
  value: any;
}
```

#### 6.3. Export & Sharing
```typescript
// Data export options
interface ExportRequest {
  reportId: number;
  format: 'csv' | 'excel' | 'pdf';
  dateRange: { start: number; end: number };
  includeRawData: boolean;
}

// Report sharing
interface SharedReport {
  id: number;
  reportId: number;
  shareToken: string;
  expiresAt: number;
  isPublic: boolean;
  allowedEmails?: string[];
}
```

## 🔧 Implementation Guidelines

### 1. API Design Patterns

#### 1.1. RESTful Conventions
```typescript
// Resource naming
GET    /marketing/campaigns           # List campaigns
POST   /marketing/campaigns           # Create campaign
GET    /marketing/campaigns/:id       # Get campaign
PUT    /marketing/campaigns/:id       # Update campaign
DELETE /marketing/campaigns/:id       # Delete campaign

// Nested resources
GET    /marketing/campaigns/:id/analytics
POST   /marketing/campaigns/:id/duplicate
POST   /marketing/campaigns/:id/test-send
```

#### 1.2. Bulk Operations
```typescript
// Bulk endpoints
POST   /marketing/audiences/bulk      # Bulk create
PUT    /marketing/audiences/bulk      # Bulk update
DELETE /marketing/audiences/bulk      # Bulk delete

// Batch processing
POST   /marketing/campaigns/batch-send
POST   /marketing/segments/batch-update
```

#### 1.3. Search & Filtering
```typescript
// Advanced search
GET /marketing/campaigns?search=keyword&status=active&platform=email
GET /marketing/audiences?tags=vip,premium&created_after=2024-01-01
GET /marketing/segments?has_audiences=true&updated_since=1234567890
```

### 2. Error Handling

#### 2.1. User-Specific Error Codes
```typescript
// Marketing-specific errors
enum MarketingErrorCode {
  CAMPAIGN_NOT_FOUND = 'MARKETING_001',
  AUDIENCE_NOT_FOUND = 'MARKETING_002',
  SEGMENT_EMPTY = 'MARKETING_003',
  TEMPLATE_INVALID = 'MARKETING_004',
  QUOTA_EXCEEDED = 'MARKETING_005',
  INVALID_SCHEDULE = 'MARKETING_006',
  DUPLICATE_NAME = 'MARKETING_007',
  INSUFFICIENT_CREDITS = 'MARKETING_008'
}
```

#### 2.2. Validation Messages
```typescript
// User-friendly validation
const ValidationMessages = {
  CAMPAIGN_TITLE_REQUIRED: 'Vui lòng nhập tiêu đề chiến dịch',
  EMAIL_INVALID: 'Địa chỉ email không hợp lệ',
  SEGMENT_CRITERIA_EMPTY: 'Vui lòng thiết lập điều kiện phân đoạn',
  SCHEDULE_IN_PAST: 'Thời gian lên lịch phải trong tương lai',
  AUDIENCE_LIMIT_EXCEEDED: 'Đã vượt quá giới hạn số lượng khách hàng'
};
```

### 3. Performance Optimization

#### 3.1. Lazy Loading
```typescript
// Pagination with cursor
interface CursorPagination {
  cursor?: string;
  limit: number;
  hasNext: boolean;
  hasPrev: boolean;
}

// Infinite scroll support
GET /marketing/campaigns?cursor=abc123&limit=20
```

#### 3.2. Background Processing
```typescript
// Async operations
interface AsyncOperation {
  id: string;
  type: 'import' | 'export' | 'campaign_send' | 'segment_calculation';
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress: number; // 0-100
  result?: any;
  error?: string;
}

// Progress tracking
GET /marketing/operations/:id/status
```

## 📱 Mobile & Responsive Considerations

### 1. Mobile-First Design
```typescript
// Mobile-optimized endpoints
GET /marketing/mobile/dashboard     # Simplified dashboard
GET /marketing/mobile/campaigns     # Mobile campaign list
POST /marketing/mobile/quick-send   # Quick send functionality
```

### 2. Offline Capabilities
```typescript
// Offline support
- Cache critical data locally
- Queue actions when offline
- Sync when connection restored
- Conflict resolution strategies
```

### 3. Push Notifications
```typescript
// Real-time notifications
interface PushNotification {
  type: 'campaign_completed' | 'high_open_rate' | 'quota_warning';
  title: string;
  message: string;
  data: any;
  actionUrl?: string;
}
```

## 🎯 Future Roadmap Features

### 1. AI-Powered Features
```typescript
// AI recommendations
- Optimal send times
- Subject line optimization
- Content suggestions
- Audience predictions
- Churn prevention

// Machine learning
- Engagement scoring
- Lifetime value prediction
- Personalization optimization
- Automated A/B testing
```

### 2. Advanced Integrations
```typescript
// CRM Integration
- Salesforce connector
- HubSpot integration
- Custom CRM APIs

// E-commerce Integration
- Shopify connector
- WooCommerce integration
- Product recommendations
- Purchase tracking
```

### 3. Compliance & Privacy
```typescript
// GDPR Features
- Consent management
- Data portability
- Right to erasure
- Privacy by design

// Email Compliance
- DKIM/SPF setup
- Deliverability monitoring
- Reputation management
- Compliance reporting
```
