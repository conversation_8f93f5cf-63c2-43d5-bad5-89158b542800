# 📋 User Marketing Features - Development Rules

> Quy tắc phát triển chi tiết cho các tính năng marketing dành cho user dựa trên cấu trúc hiện tại

## 🏗️ Cấu Trúc Hiện Tại

### 1. User Marketing Module Structure
```
src/modules/marketing/user/
├── controllers/           # API Controllers
│   ├── user-tag.controller.ts
│   ├── user-audience.controller.ts
│   ├── user-segment.controller.ts
│   ├── user-campaign.controller.ts
│   ├── user-template-email.controller.ts
│   ├── email-campaign.controller.ts
│   ├── marketing-overview.controller.ts
│   └── zalo-*.controller.ts (Zalo integration)
├── services/             # Business Logic
├── entities/             # Database Models
├── dto/                  # Data Transfer Objects
├── repositories/         # Data Access Layer
└── migrations/           # Database Migrations
```

### 2. Current API Endpoints
```
Base URL: /v1/user/marketing/

Core Features:
- GET/POST/PUT/DELETE /tags
- GET/POST/PUT/DELETE /audiences  
- GET/POST/PUT/DELETE /segments
- GET/POST/PUT/DELETE /campaigns
- GET/POST/PUT/DELETE /templates

Email Marketing:
- POST /email-campaigns
- GET /email-campaigns
- GET /email-campaigns/recent

Analytics:
- GET /overview
- GET /overview/recent-templates
- GET /statistics

Zalo Integration:
- GET/POST /zalo/*
```

## 🎯 Core User Features Rules

### 1. Tag Management (Quản lý Nhãn)

#### 1.1. Business Rules
```typescript
// Tag entity constraints
- name: required, max 255 chars, unique per user
- color: optional, default '#3498db', hex format
- userId: required, auto-set from JWT
- createdAt/updatedAt: auto-managed

// Validation rules
@IsNotEmpty({ message: 'Tên tag không được để trống' })
@IsString({ message: 'Tên tag phải là chuỗi' })
@MaxLength(255, { message: 'Tên tag không được vượt quá 255 ký tự' })
name: string;

@IsOptional()
@IsHexColor({ message: 'Màu sắc phải là mã hex hợp lệ' })
color?: string;
```

#### 1.2. API Endpoints
```typescript
// GET /marketing/tags - Lấy danh sách tag
async findAll(@CurrentUser() user: JwtPayload): Promise<AppApiResponse<TagResponseDto[]>>

// POST /marketing/tags - Tạo tag mới  
async create(@CurrentUser() user: JwtPayload, @Body() createTagDto: CreateTagDto)

// PUT /marketing/tags/:id - Cập nhật tag
async update(@CurrentUser() user: JwtPayload, @Param('id') id: string, @Body() updateTagDto: UpdateTagDto)

// DELETE /marketing/tags/:id - Xóa tag
async remove(@CurrentUser() user: JwtPayload, @Param('id') id: string)
```

#### 1.3. Security Rules
- **User Isolation**: Luôn filter theo `userId` từ JWT
- **Ownership Validation**: Kiểm tra tag thuộc về user trước khi thao tác
- **No Cross-User Access**: User chỉ xem/sửa tag của mình

### 2. Audience Management (Quản lý Đối tượng)

#### 2.1. Business Rules
```typescript
// Audience entity constraints
- email: required, valid email format, unique per user
- phone: optional, valid phone format
- userId: required, auto-set from JWT
- customFields: optional, JSONB array
- tagIds: optional, array of tag IDs

// Validation rules
@IsNotEmpty({ message: 'Email không được để trống' })
@IsEmail({}, { message: 'Email không hợp lệ' })
email: string;

@IsOptional()
@IsPhoneNumber(undefined, { message: 'Số điện thoại không hợp lệ' })
phone?: string;

@IsOptional()
@ValidateNested({ each: true })
@Type(() => CreateCustomFieldDto)
customFields?: CreateCustomFieldDto[];
```

#### 2.2. Advanced Features
```typescript
// Bulk operations
- POST /audiences/bulk - Import từ CSV/Excel
- DELETE /audiences/bulk - Xóa nhiều audience
- PUT /audiences/bulk - Cập nhật hàng loạt

// Search & Filter
- GET /audiences?email=xxx&phone=xxx&tagId=xxx
- GET /audiences?customFieldName=xxx&customFieldValue=xxx
- Pagination: page, limit, sortBy, sortDirection

// Custom Fields Support
- Dynamic custom fields per audience
- Validation theo field type (string, number, date, boolean)
- Search theo custom field values
```

#### 2.3. Integration Rules
```typescript
// Import/Export
- Support CSV, Excel formats
- Validate data before import
- Error reporting cho failed imports
- Background processing cho large datasets

// API Integration
- Webhook support cho external systems
- REST API cho third-party integrations
- Rate limiting để tránh spam
```

### 3. Segment Management (Quản lý Phân đoạn)

#### 3.1. Business Rules
```typescript
// Segment criteria structure
interface SegmentCriteria {
  operator: 'AND' | 'OR';
  conditions: SegmentCondition[];
}

interface SegmentCondition {
  field: string;           // 'email', 'phone', 'customField.name'
  operator: string;        // 'equals', 'contains', 'startsWith', 'endsWith', 'gt', 'lt'
  value: any;             // Giá trị so sánh
  type: 'string' | 'number' | 'date' | 'boolean';
}
```

#### 3.2. Dynamic Segmentation
```typescript
// Real-time audience calculation
async getAudiencesInSegment(userId: number, segment: UserSegment): Promise<UserAudience[]> {
  // 1. Lấy tất cả audience của user
  // 2. Apply segment criteria
  // 3. Return filtered results
}

// Segment preview
async previewSegment(userId: number, criteria: SegmentCriteria): Promise<{
  count: number;
  sampleAudiences: UserAudience[];
}> {
  // Preview trước khi save segment
}
```

#### 3.3. Performance Rules
```typescript
// Caching strategy
- Cache segment results for 5 minutes
- Invalidate cache khi audience data changes
- Use Redis for caching

// Query optimization
- Index trên userId, email, phone
- Limit segment preview to 100 samples
- Background calculation cho large segments
```

### 4. Campaign Management (Quản lý Chiến dịch)

#### 4.1. Campaign Types
```typescript
enum CampaignPlatform {
  EMAIL = 'email',
  SMS = 'sms', 
  ZALO = 'zalo',
  PUSH = 'push'
}

enum CampaignStatus {
  DRAFT = 'draft',
  SCHEDULED = 'scheduled', 
  SENDING = 'sending',
  SENT = 'sent',
  PAUSED = 'paused',
  CANCELLED = 'cancelled',
  COMPLETED = 'completed'
}
```

#### 4.2. Campaign Execution Rules
```typescript
// Scheduling
- Support immediate send hoặc scheduled send
- Timezone handling cho scheduled campaigns
- Automatic status updates

// Audience Selection
- Support segment-based hoặc manual audience selection
- Validate audience exists và belongs to user
- Prevent duplicate sends to same audience

// Content Management
- Template-based content hoặc custom content
- Placeholder replacement cho personalization
- Content validation trước khi send
```

#### 4.3. Queue Processing
```typescript
// Email campaigns
- Use BullMQ cho background processing
- Batch processing để tránh rate limits
- Retry mechanism cho failed sends
- Progress tracking

// Job structure
interface EmailMarketingJobDto {
  campaignId: number;
  audienceId: number;
  email: string;
  subject: string;
  content: string;
  customFields: any[];
  server: any;
  trackingId: string;
  createdAt: number;
}
```

### 5. Template Management (Quản lý Mẫu)

#### 5.1. Email Templates
```typescript
// Template structure
interface EmailTemplate {
  id: number;
  userId: number;
  name: string;
  subject: string;
  content: string;        // HTML content
  placeholders: string[]; // Available placeholders
  category?: string;
  status: 'active' | 'inactive' | 'draft';
  createdAt: number;
  updatedAt: number;
}
```

#### 5.2. Template Features
```typescript
// Placeholder system
- {{firstName}}, {{lastName}}, {{email}}, {{phone}}
- Custom field placeholders: {{customField.fieldName}}
- System placeholders: {{unsubscribeLink}}, {{trackingPixel}}

// Template validation
- HTML validation
- Placeholder validation
- Preview functionality
- Test send capability
```

#### 5.3. Template Categories
```typescript
// Pre-defined categories
enum TemplateCategory {
  WELCOME = 'welcome',
  PROMOTIONAL = 'promotional', 
  NEWSLETTER = 'newsletter',
  TRANSACTIONAL = 'transactional',
  FOLLOW_UP = 'follow_up',
  CUSTOM = 'custom'
}
```

## 🚀 Advanced Features Rules

### 1. Email Marketing Automation

#### 1.1. Drip Campaigns
```typescript
// Multi-step email sequences
interface DripCampaign {
  id: number;
  name: string;
  steps: DripStep[];
  triggerType: 'signup' | 'purchase' | 'custom';
  isActive: boolean;
}

interface DripStep {
  stepNumber: number;
  templateId: number;
  delayDays: number;
  conditions?: SegmentCriteria;
}
```

#### 1.2. Behavioral Triggers
```typescript
// Event-based automation
enum TriggerEvent {
  USER_SIGNUP = 'user_signup',
  EMAIL_OPENED = 'email_opened', 
  LINK_CLICKED = 'link_clicked',
  PURCHASE_MADE = 'purchase_made',
  CART_ABANDONED = 'cart_abandoned'
}

// Automation rules
interface AutomationRule {
  id: number;
  name: string;
  triggerEvent: TriggerEvent;
  conditions: SegmentCriteria;
  actions: AutomationAction[];
  isActive: boolean;
}
```

### 2. Analytics & Reporting

#### 2.1. Campaign Analytics
```typescript
// Email metrics
interface EmailCampaignStats {
  campaignId: number;
  sentCount: number;
  deliveredCount: number;
  openCount: number;
  clickCount: number;
  bounceCount: number;
  unsubscribeCount: number;
  
  // Calculated metrics
  deliveryRate: number;    // (delivered/sent) * 100
  openRate: number;        // (opened/delivered) * 100  
  clickRate: number;       // (clicked/opened) * 100
  bounceRate: number;      // (bounced/sent) * 100
  unsubscribeRate: number; // (unsubscribed/delivered) * 100
}
```

#### 2.2. Real-time Tracking
```typescript
// Tracking events
enum EmailEvent {
  SENT = 'sent',
  DELIVERED = 'delivered',
  OPENED = 'opened', 
  CLICKED = 'clicked',
  BOUNCED = 'bounced',
  UNSUBSCRIBED = 'unsubscribed',
  SPAM_REPORTED = 'spam_reported'
}

// Event logging
interface EmailEventLog {
  id: number;
  campaignId: number;
  audienceId: number;
  event: EmailEvent;
  timestamp: number;
  metadata?: any;
}
```

### 3. Integration Features

#### 3.1. Zalo Integration (Hiện có)
```typescript
// Zalo features đã implement
- Zalo Official Account management
- ZNS (Zalo Notification Service) templates
- Zalo message sending
- Zalo follower management
- Zalo automation campaigns
- Zalo segment targeting
```

#### 3.2. Future Integrations
```typescript
// SMS Integration
interface SmsProvider {
  type: 'twilio' | 'nexmo' | 'infobip';
  apiKey: string;
  senderId: string;
  isActive: boolean;
}

// Social Media Integration
interface SocialMediaAccount {
  platform: 'facebook' | 'instagram' | 'twitter';
  accountId: string;
  accessToken: string;
  isActive: boolean;
}
```

## 📊 Performance & Scalability Rules

### 1. Database Optimization
```typescript
// Required indexes
- user_campaigns: (userId, status, platform, createdAt)
- user_audience: (userId, email, phone, createdAt)
- user_segments: (userId, createdAt)
- user_campaign_history: (campaignId, audienceId, status)

// Query patterns
- Always filter by userId first
- Use pagination for large datasets
- Implement query result caching
- Use database connection pooling
```

### 2. Queue Management
```typescript
// Queue configuration
- Separate queues cho different platforms
- Priority queues cho urgent campaigns
- Dead letter queues cho failed jobs
- Rate limiting per user/platform

// Monitoring
- Job success/failure rates
- Queue length monitoring
- Processing time metrics
- Error alerting
```

### 3. Caching Strategy
```typescript
// Cache layers
- Redis cho session data
- Application cache cho template data
- Database query result cache
- CDN cho static assets

// Cache invalidation
- Time-based expiration
- Event-based invalidation
- Manual cache clearing
- Cache warming strategies
```

## 🔒 Security & Compliance Rules

### 1. Data Protection
```typescript
// GDPR Compliance
- User consent management
- Data export functionality
- Data deletion (right to be forgotten)
- Audit logging

// Email compliance
- Unsubscribe links in all emails
- Bounce handling
- Spam complaint handling
- Sender reputation management
```

### 2. Rate Limiting
```typescript
// API rate limits
- 1000 requests/hour per user
- 100 campaigns/day per user
- 10,000 emails/day per user (configurable)
- Burst protection

// Email sending limits
- Respect ESP rate limits
- Implement backoff strategies
- Monitor sender reputation
- Automatic throttling
```

### 3. Input Validation
```typescript
// Sanitization rules
- HTML content sanitization
- Email address validation
- Phone number validation
- File upload validation

// Business logic validation
- Audience ownership validation
- Template usage validation
- Campaign scheduling validation
- Resource quota validation
```

## ✅ Development Checklist

### Before Adding New Features:
- [ ] Check existing entities can be extended
- [ ] Follow user isolation patterns
- [ ] Implement proper validation
- [ ] Add comprehensive error handling
- [ ] Include audit logging
- [ ] Write unit tests
- [ ] Update API documentation
- [ ] Consider performance impact
- [ ] Plan migration strategy
- [ ] Review security implications

### Code Quality Standards:
- [ ] Use TypeScript strict mode
- [ ] Follow naming conventions
- [ ] Implement proper error codes
- [ ] Add comprehensive logging
- [ ] Use transactions for data consistency
- [ ] Implement proper pagination
- [ ] Add input sanitization
- [ ] Include rate limiting
- [ ] Write integration tests
- [ ] Document API endpoints
