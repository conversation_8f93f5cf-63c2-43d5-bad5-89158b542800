import { Body, Controller, Delete, Get, Param, ParseIntPipe, Post, Put, Query, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, getSchemaPath } from '@nestjs/swagger';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import { ApiResponseDto, PaginatedResult } from '@/common/response';
import { JwtUserGuard } from '@/modules/auth/guards';
import { SWAGGER_API_TAGS } from '@/common/swagger';
import { UserTemplateEmailService } from '@modules/marketing/user/services';
import {
  CreateTemplateEmailDto,
  TemplateEmailQueryDto,
  TemplateEmailResponseDto,
  UpdateTemplateEmailDto,
  TemplateEmailOverviewResponseDto,
  DeleteMultipleTemplateEmailDto,
  DeleteMultipleTemplateEmailResultDto
} from '../dto/template-email';
import { UserTemplateEmail } from '@modules/marketing/user';

/**
 * Controller xử lý API liên quan đến template email
 */
@ApiTags(SWAGGER_API_TAGS.USER_TEMPLATE_EMAIL)
@ApiBearerAuth("JWT-auth")
@UseGuards(JwtUserGuard)
@Controller('marketing/template-emails')
export class UserTemplateEmailController {
  constructor(private readonly userTemplateEmailService: UserTemplateEmailService) {}

  /**
   * Lấy thông tin overview về template email
   */
  @Get('overview')
  @ApiOperation({
    summary: 'Lấy thông tin overview về template email',
    description: 'Lấy thống kê tổng quan về template email bao gồm tổng số templates, templates hoạt động, bản nháp, số test gửi tuần này và templates mới tuần này'
  })
  @ApiResponse({
    status: 200,
    description: 'Thông tin overview template email',
    type: TemplateEmailOverviewResponseDto
  })
  async getOverview(
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<TemplateEmailOverviewResponseDto>> {
    const result = await this.userTemplateEmailService.getOverview(user.id);
    return ApiResponseDto.success(result, 'Lấy thông tin overview template email thành công');
  }

  /**
   * Lấy danh sách template email với phân trang và filter
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách template email với phân trang và filter' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách template email với phân trang',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: {
              properties: {
                items: {
                  type: 'array',
                  items: { $ref: getSchemaPath(TemplateEmailResponseDto) }
                },
                meta: {
                  type: 'object',
                  properties: {
                    totalItems: { type: 'number' },
                    itemCount: { type: 'number' },
                    itemsPerPage: { type: 'number' },
                    totalPages: { type: 'number' },
                    currentPage: { type: 'number' }
                  }
                }
              }
            }
          }
        }
      ]
    }
  })
  async findAll(
    @CurrentUser() user: JwtPayload,
    @Query() queryDto: TemplateEmailQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<UserTemplateEmail>>> {
    const result = await this.userTemplateEmailService.findAll(user.id, queryDto);
    return ApiResponseDto.success(result, 'Lấy danh sách template email thành công');
  }

  /**
   * Lấy chi tiết template email
   */
  @Get(':id')
  @ApiOperation({ summary: 'Lấy chi tiết template email' })
  @ApiResponse({
    status: 200,
    description: 'Chi tiết template email',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { $ref: getSchemaPath(TemplateEmailResponseDto) }
          }
        }
      ]
    }
  })
  @ApiResponse({ status: 404, description: 'Template email không tồn tại' })
  async findOne(
    @CurrentUser() user: JwtPayload,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<ApiResponseDto<UserTemplateEmail>> {
    const result = await this.userTemplateEmailService.findById(id, user.id);
    return ApiResponseDto.success(result, 'Lấy chi tiết template email thành công');
  }

  /**
   * Tạo mới template email
   */
  @Post()
  @ApiOperation({
    summary: 'Tạo template email mới',
    description: 'Tạo template email mới với đầy đủ thông tin bao gồm nội dung HTML, biến động và tags. Hệ thống sẽ tự động extract placeholders từ content và validate dữ liệu.'
  })
  @ApiResponse({
    status: 201,
    description: 'Template email được tạo thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { $ref: getSchemaPath(TemplateEmailResponseDto) }
          }
        }
      ]
    }
  })
  @ApiResponse({
    status: 400,
    description: 'Dữ liệu không hợp lệ',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 400 },
        message: { type: 'string', example: 'Dữ liệu không hợp lệ' },
        errors: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              field: { type: 'string', example: 'name' },
              message: { type: 'string', example: 'Tên template là bắt buộc' }
            }
          }
        }
      }
    }
  })
  @ApiResponse({
    status: 409,
    description: 'Template với tên này đã tồn tại',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 409 },
        message: { type: 'string', example: 'Template với tên này đã tồn tại' },
        error: { type: 'string', example: 'TEMPLATE_NAME_EXISTS' }
      }
    }
  })
  async create(
    @CurrentUser() user: JwtPayload,
    @Body() createDto: CreateTemplateEmailDto,
  ): Promise<ApiResponseDto<UserTemplateEmail>> {
    const result = await this.userTemplateEmailService.create(user.id, createDto);
    return ApiResponseDto.success(result, 'Tạo template email thành công');
  }

  /**
   * Cập nhật template email
   */
  @Put(':id')
  @ApiOperation({ summary: 'Cập nhật template email' })
  @ApiResponse({
    status: 200,
    description: 'Template email đã được cập nhật',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { $ref: getSchemaPath(TemplateEmailResponseDto) }
          }
        }
      ]
    }
  })
  @ApiResponse({ status: 404, description: 'Template email không tồn tại' })
  async update(
    @CurrentUser() user: JwtPayload,
    @Param('id', ParseIntPipe) id: number,
    @Body() updateDto: UpdateTemplateEmailDto,
  ): Promise<ApiResponseDto<UserTemplateEmail>> {
    const result = await this.userTemplateEmailService.update(id, user.id, updateDto);
    return ApiResponseDto.success(result, 'Cập nhật template email thành công');
  }

  /**
   * Xóa nhiều template email
   */
  @Delete('bulk')
  @ApiOperation({
    summary: 'Xóa nhiều template email',
    description: 'Xóa nhiều template email cùng lúc. API sẽ trả về danh sách các template đã xóa thành công và thất bại với lý do cụ thể.'
  })
  @ApiResponse({
    status: 200,
    description: 'Kết quả xóa nhiều template email',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { $ref: getSchemaPath(DeleteMultipleTemplateEmailResultDto) }
          }
        }
      ]
    }
  })
  @ApiResponse({
    status: 400,
    description: 'Dữ liệu không hợp lệ',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 400 },
        message: { type: 'string', example: 'Dữ liệu không hợp lệ' },
        errors: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              field: { type: 'string', example: 'ids' },
              message: { type: 'string', example: 'Phải có ít nhất 1 ID để xóa' }
            }
          }
        }
      }
    }
  })
  async deleteMultiple(
    @CurrentUser() user: JwtPayload,
    @Body() deleteDto: DeleteMultipleTemplateEmailDto,
  ): Promise<ApiResponseDto<DeleteMultipleTemplateEmailResultDto>> {
    const result = await this.userTemplateEmailService.deleteMultiple(deleteDto.ids, user.id);

    let message = 'Xóa template email thành công';
    if (result.totalFailed > 0) {
      message = `Đã xóa ${result.totalDeleted} template thành công, ${result.totalFailed} template thất bại`;
    } else {
      message = `Đã xóa ${result.totalDeleted} template thành công`;
    }

    return ApiResponseDto.success(result, message);
  }
}
