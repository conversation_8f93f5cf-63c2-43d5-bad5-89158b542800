import { Api<PERSON><PERSON>ponseDto } from '@common/response';
import { CurrentEmployee } from '@modules/auth/decorators/current-employee.decorator';
import { JwtEmployeeGuard } from '@modules/auth/guards';
import {
  Body,
  Controller,
  Delete,
  Get,
  Logger,
  Param,
  ParseUUIDPipe,
  Post,
  Put,
  Query,
  UseGuards
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiBody,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags
} from '@nestjs/swagger';
import {
  BulkDeleteModelRegistryDto,
  BulkDeleteResponseDto,
  CreateBulkModelRegistryDto,
  ModelRegistryDetailResponseDto,
  ModelRegistryQueryDto,
  ModelRegistryResponseDto,
  UpdateModelRegistryDto
} from '../dto/model-registry';
import { AdminModelRegistryService } from '../services/admin-model-registry.service';
import { SWAGGER_API_TAGS } from '@/common/swagger';

/**
 * Controller xử lý các API admin cho Model Registry
 */
@ApiTags(SWAGGER_API_TAGS.ADMIN_MODEL_REGISTRY)
@Controller('admin/model-registry')
@UseGuards(JwtEmployeeGuard)
@ApiBearerAuth('JWT-auth')
export class AdminModelRegistryController {
  private readonly logger = new Logger(AdminModelRegistryController.name);

  constructor(
    private readonly adminModelRegistryService: AdminModelRegistryService,
  ) { }

  /**
   * API tạo nhiều model registry cùng lúc
   */
  @Post('bulk')
  @ApiOperation({
    summary: 'Tạo nhiều model registry cùng lúc',
    description: 'Tạo nhiều model registry với validation unique model_name_pattern'
  })
  @ApiBody({ type: CreateBulkModelRegistryDto })
  @ApiResponse({
    status: 201,
    description: 'Tạo thành công',
    type: [ModelRegistryResponseDto]
  })
  @ApiResponse({
    status: 400,
    description: 'Lỗi validation hoặc pattern trùng lặp',
    schema: {
      example: {
        success: false,
        errorCode: 20011,
        message: "Model name pattern 'gpt-4*' đã tồn tại trong hệ thống"
      }
    }
  })
  async bulkCreate(
    @Body() createBulkDto: CreateBulkModelRegistryDto,
    @CurrentEmployee('id') employeeId: number,
  ): Promise<ApiResponseDto<ModelRegistryResponseDto[]>> {
    this.logger.log(`Admin ${employeeId} tạo ${createBulkDto.registries.length} model registries`);

    const result = await this.adminModelRegistryService.bulkCreate(createBulkDto, employeeId);

    return ApiResponseDto.success(result, 'Tạo model registries thành công');
  }

  /**
   * API cập nhật một model registry
   */
  @Put(':id')
  @ApiOperation({
    summary: 'Cập nhật một model registry',
    description: 'Cập nhật thông tin model registry với validation unique pattern'
  })
  @ApiParam({
    name: 'id',
    description: 'UUID của model registry',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @ApiBody({ type: UpdateModelRegistryDto })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật thành công',
    type: ModelRegistryResponseDto
  })
  @ApiResponse({
    status: 404,
    description: 'Không tìm thấy model registry',
    schema: {
      example: {
        success: false,
        errorCode: 20010,
        message: "Không tìm thấy model registry với ID: 123e4567-e89b-12d3-a456-************"
      }
    }
  })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateDto: UpdateModelRegistryDto,
    @CurrentEmployee('id') employeeId: number,
  ): Promise<ApiResponseDto<ModelRegistryResponseDto>> {
    this.logger.log(`Admin ${employeeId} cập nhật model registry ${id}`);

    const result = await this.adminModelRegistryService.update(id, updateDto, employeeId);

    return ApiResponseDto.success(result, 'Cập nhật model registry thành công');
  }

  /**
   * API xóa mềm nhiều model registry cùng lúc
   */
  @Delete('bulk')
  @ApiOperation({
    summary: 'Xóa mềm nhiều model registry cùng lúc',
    description: 'Xóa mềm nhiều model registry, trả về danh sách thành công và thất bại'
  })
  @ApiBody({ type: BulkDeleteModelRegistryDto })
  @ApiResponse({
    status: 200,
    description: 'Xóa thành công (có thể một phần)',
    type: BulkDeleteResponseDto
  })
  async bulkDelete(
    @Body() bulkDeleteDto: BulkDeleteModelRegistryDto,
    @CurrentEmployee('id') employeeId: number,
  ): Promise<ApiResponseDto<BulkDeleteResponseDto>> {
    this.logger.log(`Admin ${employeeId} xóa ${bulkDeleteDto.ids.length} model registries`);

    const result = await this.adminModelRegistryService.bulkDelete(bulkDeleteDto, employeeId);

    return ApiResponseDto.success(result, 'Xóa model registries hoàn tất');
  }

  /**
   * API lấy danh sách model registry với phân trang
   */
  @Get()
  @ApiOperation({
    summary: 'Lấy danh sách model registry với phân trang',
    description: 'Lấy danh sách model registry với tìm kiếm, lọc và phân trang'
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách thành công',
    schema: {
      example: {
        success: true,
        data: {
          items: [],
          meta: {
            totalItems: 100,
            itemCount: 10,
            itemsPerPage: 10,
            totalPages: 10,
            currentPage: 1
          }
        },
        message: 'Lấy danh sách model registries thành công'
      }
    }
  })
  async findAll(
    @Query() queryDto: ModelRegistryQueryDto,
  ): Promise<ApiResponseDto<any>> {
    this.logger.log(`Lấy danh sách model registries với query: ${JSON.stringify(queryDto)}`);

    const result = await this.adminModelRegistryService.findAll(queryDto);

    return ApiResponseDto.paginated(result, 'Lấy danh sách model registries thành công');
  }

  /**
   * API lấy chi tiết một model registry
   */
  @Get(':id')
  @ApiOperation({
    summary: 'Lấy chi tiết một model registry',
    description: 'Lấy thông tin chi tiết của model registry bao gồm thông tin người tạo/cập nhật'
  })
  @ApiParam({
    name: 'id',
    description: 'UUID của model registry',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy chi tiết thành công',
    type: ModelRegistryDetailResponseDto
  })
  @ApiResponse({
    status: 404,
    description: 'Không tìm thấy model registry',
    schema: {
      example: {
        success: false,
        errorCode: 20010,
        message: "Không tìm thấy model registry với ID: 123e4567-e89b-12d3-a456-************"
      }
    }
  })
  async findById(
    @Param('id', ParseUUIDPipe) id: string,
  ): Promise<ApiResponseDto<ModelRegistryDetailResponseDto>> {
    this.logger.log(`Lấy chi tiết model registry ${id}`);

    const result = await this.adminModelRegistryService.findById(id);

    return ApiResponseDto.success(result, 'Lấy chi tiết model registry thành công');
  }
}
