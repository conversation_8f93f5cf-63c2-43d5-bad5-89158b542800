import { Controller, Get, Post, Query, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
import { JwtEmployeeGuard } from '@modules/auth/guards';
import { ApiResponseDto } from '@common/response';
import { PerformanceMonitorService, BenchmarkResult } from '../../services/performance-monitor.service';
import { PatternMatchingEngineService } from '../../services/pattern-matching-engine.service';
import { BulkModelOperationsService } from '../../services/bulk-model-operations.service';
import { SWAGGER_API_TAGS } from '@/common/swagger';

/**
 * Controller cho performance monitoring và benchmarking
 */
@Controller('admin/models/performance')
@ApiTags(SWAGGER_API_TAGS.ADMIN_MODEL_PERFORMANCE)
@UseGuards(JwtEmployeeGuard)
@ApiBearerAuth('JWT-auth')
export class AdminPerformanceController {
  constructor(
    private readonly performanceMonitorService: PerformanceMonitorService,
    private readonly patternMatchingEngine: PatternMatchingEngineService,
    private readonly bulkModelOperations: BulkModelOperationsService
  ) {}

  /**
   * Chạy benchmark cho syncModels với các kích thước khác nhau
   */
  @Post('benchmark/sync-models')
  @ApiOperation({ 
    summary: 'Chạy benchmark cho syncModels',
    description: 'Thực hiện benchmark để đo lường performance của syncModels với các kích thước models khác nhau'
  })
  @ApiQuery({
    name: 'modelCounts',
    description: 'Danh sách số lượng models để test (comma-separated)',
    example: '10,50,100',
    required: false
  })
  @ApiQuery({
    name: 'provider',
    description: 'Provider để test',
    example: 'openai',
    required: false
  })
  @ApiResponse({
    status: 200,
    description: 'Benchmark results',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        data: {
          type: 'object',
          properties: {
            results: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  name: { type: 'string' },
                  modelCount: { type: 'number' },
                  provider: { type: 'string' },
                  throughput: { type: 'number' },
                  avgTimePerModel: { type: 'number' },
                  memoryEfficiency: { type: 'number' },
                  queriesPerModel: { type: 'number' },
                  metrics: {
                    type: 'object',
                    properties: {
                      duration: { type: 'number' },
                      memoryBefore: { type: 'number' },
                      memoryAfter: { type: 'number' },
                      peakMemory: { type: 'number' },
                      databaseQueries: { type: 'number' },
                      modelsProcessed: { type: 'number' },
                      errors: { type: 'array', items: { type: 'string' } }
                    }
                  }
                }
              }
            },
            report: { type: 'string' },
            summary: {
              type: 'object',
              properties: {
                totalTests: { type: 'number' },
                avgThroughput: { type: 'number' },
                avgMemoryUsage: { type: 'number' },
                totalErrors: { type: 'number' },
                bottlenecks: { type: 'array', items: { type: 'string' } }
              }
            }
          }
        }
      }
    }
  })
  async runSyncModelsBenchmark(
    @Query('modelCounts') modelCountsStr?: string,
    @Query('provider') provider: string = 'openai'
  ): Promise<ApiResponseDto<{
    results: BenchmarkResult[];
    report: string;
    summary: {
      totalTests: number;
      avgThroughput: number;
      avgMemoryUsage: number;
      totalErrors: number;
      bottlenecks: string[];
    };
  }>> {
    try {
      // Parse model counts
      const modelCounts = modelCountsStr 
        ? modelCountsStr.split(',').map(count => parseInt(count.trim(), 10)).filter(count => !isNaN(count))
        : [10, 25, 50, 100];

      // Validate input
      if (modelCounts.length === 0) {
        throw new Error('Invalid model counts provided');
      }

      if (modelCounts.some(count => count <= 0 || count > 1000)) {
        throw new Error('Model counts must be between 1 and 1000');
      }

      // Run benchmark
      const results = await this.performanceMonitorService.runSyncModelsBenchmark(
        modelCounts,
        provider
      );

      // Generate report
      const report = this.performanceMonitorService.generatePerformanceReport(results);

      // Calculate summary
      const summary = this.calculateSummary(results);

      return ApiResponseDto.success({
        results,
        report,
        summary
      });

    } catch (error) {
      throw new Error(`Benchmark failed: ${error.message}`);
    }
  }

  /**
   * Get performance metrics cho active operations
   */
  @Get('metrics/active')
  @ApiOperation({ 
    summary: 'Lấy performance metrics cho active operations',
    description: 'Hiển thị metrics của các operations đang chạy'
  })
  @ApiResponse({
    status: 200,
    description: 'Active performance metrics',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        data: {
          type: 'object',
          properties: {
            activeOperations: { type: 'number' },
            operations: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  operationId: { type: 'string' },
                  startTime: { type: 'number' },
                  duration: { type: 'number' },
                  memoryUsage: { type: 'number' },
                  modelsProcessed: { type: 'number' },
                  metadata: { type: 'object' }
                }
              }
            }
          }
        }
      }
    }
  })
  async getActiveMetrics(): Promise<ApiResponseDto<{
    activeOperations: number;
    operations: Array<{
      operationId: string;
      startTime: number;
      duration: number;
      memoryUsage: number;
      modelsProcessed: number;
      metadata: Record<string, any>;
    }>;
  }>> {
    try {
      // Get active operations from service
      const activeOperations = this.performanceMonitorService['activeMonitors'];
      const operations: Array<{
        operationId: string;
        startTime: number;
        duration: number;
        memoryUsage: number;
        modelsProcessed: number;
        metadata: Record<string, any>;
      }> = [];

      for (const [operationId, metrics] of activeOperations.entries()) {
        operations.push({
          operationId,
          startTime: metrics.startTime,
          duration: Date.now() - metrics.startTime,
          memoryUsage: this.getCurrentMemoryUsage(),
          modelsProcessed: metrics.modelsProcessed,
          metadata: metrics.metadata
        });
      }

      return ApiResponseDto.success({
        activeOperations: operations.length,
        operations
      });

    } catch (error) {
      throw new Error(`Failed to get active metrics: ${error.message}`);
    }
  }

  /**
   * Get system performance overview
   */
  @Get('system/overview')
  @ApiOperation({ 
    summary: 'Lấy system performance overview',
    description: 'Hiển thị tổng quan performance của hệ thống'
  })
  @ApiResponse({
    status: 200,
    description: 'System performance overview'
  })
  async getSystemOverview(): Promise<ApiResponseDto<{
    memory: {
      used: number;
      total: number;
      percentage: number;
    };
    cpu: {
      usage: number;
    };
    uptime: number;
    nodeVersion: string;
    recommendations: string[];
  }>> {
    try {
      const memoryUsage = process.memoryUsage();
      const totalMemory = memoryUsage.heapTotal;
      const usedMemory = memoryUsage.heapUsed;
      
      const overview = {
        memory: {
          used: Math.round(usedMemory / 1024 / 1024 * 100) / 100, // MB
          total: Math.round(totalMemory / 1024 / 1024 * 100) / 100, // MB
          percentage: Math.round((usedMemory / totalMemory) * 100)
        },
        cpu: {
          usage: process.cpuUsage().user / 1000000 // Convert to seconds
        },
        uptime: Math.round(process.uptime()),
        nodeVersion: process.version,
        recommendations: this.generateRecommendations(memoryUsage)
      };

      return ApiResponseDto.success(overview);

    } catch (error) {
      throw new Error(`Failed to get system overview: ${error.message}`);
    }
  }

  /**
   * Calculate summary from benchmark results
   */
  private calculateSummary(results: BenchmarkResult[]): {
    totalTests: number;
    avgThroughput: number;
    avgMemoryUsage: number;
    totalErrors: number;
    bottlenecks: string[];
  } {
    if (results.length === 0) {
      return {
        totalTests: 0,
        avgThroughput: 0,
        avgMemoryUsage: 0,
        totalErrors: 0,
        bottlenecks: []
      };
    }

    const totalThroughput = results.reduce((sum, result) => sum + result.throughput, 0);
    const totalMemoryUsage = results.reduce((sum, result) => 
      sum + (result.metrics.peakMemory - result.metrics.memoryBefore), 0);
    const totalErrors = results.reduce((sum, result) => sum + result.metrics.errors.length, 0);

    // Identify bottlenecks
    const bottlenecks: string[] = [];
    results.forEach(result => {
      if (result.throughput < 10) {
        bottlenecks.push(`Low throughput: ${result.throughput.toFixed(1)} models/sec (${result.modelCount} models)`);
      }
      if (result.queriesPerModel > 5) {
        bottlenecks.push(`High DB queries: ${result.queriesPerModel.toFixed(1)} queries/model (${result.modelCount} models)`);
      }
      if (result.memoryEfficiency > 1) {
        bottlenecks.push(`High memory usage: ${result.memoryEfficiency.toFixed(1)} MB/model (${result.modelCount} models)`);
      }
    });

    return {
      totalTests: results.length,
      avgThroughput: totalThroughput / results.length,
      avgMemoryUsage: totalMemoryUsage / results.length,
      totalErrors,
      bottlenecks: [...new Set(bottlenecks)] // Remove duplicates
    };
  }

  /**
   * Get current memory usage
   */
  private getCurrentMemoryUsage(): number {
    const usage = process.memoryUsage();
    return Math.round(usage.heapUsed / 1024 / 1024 * 100) / 100; // MB
  }

  /**
   * Generate performance recommendations
   */
  private generateRecommendations(memoryUsage: NodeJS.MemoryUsage): string[] {
    const recommendations: string[] = [];
    
    const heapUsedMB = memoryUsage.heapUsed / 1024 / 1024;
    const heapTotalMB = memoryUsage.heapTotal / 1024 / 1024;
    const memoryPercentage = (memoryUsage.heapUsed / memoryUsage.heapTotal) * 100;

    if (memoryPercentage > 80) {
      recommendations.push('High memory usage detected. Consider implementing memory optimization.');
    }

    if (heapUsedMB > 500) {
      recommendations.push('Large heap size detected. Monitor for memory leaks.');
    }

    if (recommendations.length === 0) {
      recommendations.push('System performance is within normal parameters.');
    }

    return recommendations;
  }
}
