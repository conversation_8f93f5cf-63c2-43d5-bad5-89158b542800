import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { SystemModelKeyLlm } from '../entities/system-model-key-llm.entity';

/**
 * Repository cho SystemModelKeyLlm
 * Xử lý các thao tác CRUD và truy vấn nâng cao liên quan đến mapping system models và keys
 */
@Injectable()
export class SystemModelKeyLlmRepository extends Repository<SystemModelKeyLlm> {
  private readonly logger = new Logger(SystemModelKeyLlmRepository.name);

  constructor(private dataSource: DataSource) {
    super(SystemModelKeyLlm, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder cơ bản cho SystemModelKeyLlm
   * @returns SelectQueryBuilder cho SystemModelKeyLlm
   */
  createBaseQuery(): SelectQueryBuilder<SystemModelKeyLlm> {
    return this.createQueryBuilder('systemModelKeyLlm')
      .select([
        'systemModelKeyLlm.modelId',
        'systemModelKeyLlm.llmKeyId'
      ]);
  }

  /**
   * Tìm mapping theo model ID và key ID
   * @param modelId ID của system model
   * @param llmKeyId ID của system key
   * @returns Mapping hoặc null
   */
  async findByModelAndKey(modelId: string, llmKeyId: string): Promise<SystemModelKeyLlm | null> {
    return this.findOne({
      where: { modelId, llmKeyId }
    });
  }

  /**
   * Tìm tất cả mappings theo key ID
   * @param llmKeyId ID của system key
   * @returns Danh sách mappings
   */
  async findByKeyId(llmKeyId: string): Promise<SystemModelKeyLlm[]> {
    return this.find({
      where: { llmKeyId }
    });
  }

  /**
   * Tìm tất cả mappings theo model ID
   * @param modelId ID của system model
   * @returns Danh sách mappings
   */
  async findByModelId(modelId: string): Promise<SystemModelKeyLlm[]> {
    return this.find({
      where: { modelId }
    });
  }

  /**
   * Tìm tất cả model IDs theo key ID
   * @param llmKeyId ID của system key
   * @returns Danh sách model IDs
   */
  async findModelIdsByKeyId(llmKeyId: string): Promise<string[]> {
    const mappings = await this.createBaseQuery()
      .where('systemModelKeyLlm.llmKeyId = :llmKeyId', { llmKeyId })
      .getMany();

    return mappings.map(mapping => mapping.modelId);
  }

  /**
   * Tìm tất cả key IDs theo model ID
   * @param modelId ID của system model
   * @returns Danh sách key IDs
   */
  async findKeyIdsByModelId(modelId: string): Promise<string[]> {
    const mappings = await this.createBaseQuery()
      .where('systemModelKeyLlm.modelId = :modelId', { modelId })
      .getMany();

    return mappings.map(mapping => mapping.llmKeyId);
  }

  /**
   * Bulk insert mappings
   * @param mappings Danh sách mappings để insert
   * @returns Danh sách mappings đã được lưu
   */
  async bulkInsert(mappings: Partial<SystemModelKeyLlm>[]): Promise<SystemModelKeyLlm[]> {
    if (!mappings || mappings.length === 0) {
      return [];
    }

    try {
      const entities = mappings.map(mapping => this.create(mapping));
      return await this.save(entities);
    } catch (error) {
      this.logger.error(`Failed to bulk insert system model key mappings: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Bulk upsert mappings (insert hoặc ignore nếu đã tồn tại)
   * @param mappings Danh sách mappings để upsert
   * @returns Kết quả upsert
   */
  async bulkUpsert(mappings: Partial<SystemModelKeyLlm>[]): Promise<{
    inserted: SystemModelKeyLlm[];
    skipped: number;
  }> {
    if (!mappings || mappings.length === 0) {
      return { inserted: [], skipped: 0 };
    }

    const inserted: SystemModelKeyLlm[] = [];
    let skipped = 0;

    for (const mappingData of mappings) {
      try {
        const existingMapping = await this.findByModelAndKey(
          mappingData.modelId!,
          mappingData.llmKeyId!
        );
        
        if (!existingMapping) {
          // Insert new mapping
          const newMapping = this.create(mappingData);
          const savedMapping = await this.save(newMapping);
          inserted.push(savedMapping);
        } else {
          // Skip existing mapping
          skipped++;
        }
      } catch (error) {
        this.logger.error(`Failed to upsert system model key mapping ${mappingData.modelId} -> ${mappingData.llmKeyId}: ${error.message}`, error.stack);
        throw error;
      }
    }

    this.logger.log(`Bulk upsert completed: ${inserted.length} inserted, ${skipped} skipped`);
    return { inserted, skipped };
  }

  /**
   * Kiểm tra mapping tồn tại
   * @param modelId ID của system model
   * @param llmKeyId ID của system key
   * @returns True nếu tồn tại
   */
  async existsByModelAndKey(modelId: string, llmKeyId: string): Promise<boolean> {
    const count = await this.count({
      where: { modelId, llmKeyId }
    });
    return count > 0;
  }

  /**
   * Đếm số mappings theo key ID
   * @param llmKeyId ID của system key
   * @returns Số lượng mappings
   */
  async countByKeyId(llmKeyId: string): Promise<number> {
    return this.count({
      where: { llmKeyId }
    });
  }

  /**
   * Đếm số mappings theo model ID
   * @param modelId ID của system model
   * @returns Số lượng mappings
   */
  async countByModelId(modelId: string): Promise<number> {
    return this.count({
      where: { modelId }
    });
  }

  /**
   * Xóa mappings theo key ID
   * @param llmKeyId ID của system key
   * @returns Số lượng records đã xóa
   */
  async deleteByKeyId(llmKeyId: string): Promise<number> {
    const result = await this.createQueryBuilder()
      .delete()
      .where('llmKeyId = :llmKeyId', { llmKeyId })
      .execute();

    return result.affected || 0;
  }

  /**
   * Xóa mappings theo model ID
   * @param modelId ID của system model
   * @returns Số lượng records đã xóa
   */
  async deleteByModelId(modelId: string): Promise<number> {
    const result = await this.createQueryBuilder()
      .delete()
      .where('modelId = :modelId', { modelId })
      .execute();

    return result.affected || 0;
  }

  /**
   * Xóa mappings theo danh sách key IDs
   * @param llmKeyIds Danh sách key IDs
   * @returns Số lượng records đã xóa
   */
  async deleteByKeyIds(llmKeyIds: string[]): Promise<number> {
    if (!llmKeyIds || llmKeyIds.length === 0) {
      return 0;
    }

    const result = await this.createQueryBuilder()
      .delete()
      .where('llmKeyId IN (:...llmKeyIds)', { llmKeyIds })
      .execute();

    return result.affected || 0;
  }

  /**
   * Xóa mappings theo danh sách model IDs
   * @param modelIds Danh sách model IDs
   * @returns Số lượng records đã xóa
   */
  async deleteByModelIds(modelIds: string[]): Promise<number> {
    if (!modelIds || modelIds.length === 0) {
      return 0;
    }

    const result = await this.createQueryBuilder()
      .delete()
      .where('modelId IN (:...modelIds)', { modelIds })
      .execute();

    return result.affected || 0;
  }

  /**
   * Lấy thống kê mappings
   * @returns Thống kê tổng quan
   */
  async getStats(): Promise<{
    totalMappings: number;
    uniqueModels: number;
    uniqueKeys: number;
  }> {
    const totalMappings = await this.count();
    
    const uniqueModelsResult = await this.createQueryBuilder('systemModelKeyLlm')
      .select('COUNT(DISTINCT systemModelKeyLlm.modelId)', 'count')
      .getRawOne();
    
    const uniqueKeysResult = await this.createQueryBuilder('systemModelKeyLlm')
      .select('COUNT(DISTINCT systemModelKeyLlm.llmKeyId)', 'count')
      .getRawOne();

    return {
      totalMappings,
      uniqueModels: parseInt(uniqueModelsResult.count),
      uniqueKeys: parseInt(uniqueKeysResult.count),
    };
  }

  /**
   * Lấy mappings với thông tin chi tiết model và key
   * @param llmKeyId ID của system key (optional)
   * @param modelId ID của system model (optional)
   * @returns Danh sách mappings với thông tin chi tiết
   */
  async findWithDetails(llmKeyId?: string, modelId?: string): Promise<Array<{
    modelId: string;
    llmKeyId: string;
    modelName: string;
    keyName: string;
    provider: string;
  }>> {
    const query = this.createQueryBuilder('sml')
      .leftJoin('system_models', 'sm', 'sml.modelId = sm.id')
      .leftJoin('system_key_llm', 'skl', 'sml.llmKeyId = skl.id')
      .select([
        'sml.modelId',
        'sml.llmKeyId',
        'sm.modelId as modelName',
        'skl.name as keyName',
        'skl.provider as provider'
      ]);

    if (llmKeyId) {
      query.andWhere('sml.llmKeyId = :llmKeyId', { llmKeyId });
    }

    if (modelId) {
      query.andWhere('sml.modelId = :modelId', { modelId });
    }

    const results = await query.getRawMany();

    return results.map(result => ({
      modelId: result.sml_modelId,
      llmKeyId: result.sml_llmKeyId,
      modelName: result.modelName,
      keyName: result.keyName,
      provider: result.provider,
    }));
  }
}
