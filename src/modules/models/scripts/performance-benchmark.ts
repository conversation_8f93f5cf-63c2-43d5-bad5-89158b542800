import { NestFactory } from '@nestjs/core';
import { AppModule } from '../../../app.module';
import { PerformanceMonitorService } from '../services/performance-monitor.service';
import { ModelDiscoveryService } from '../services/model-discovery.service';
import { SystemModelSyncService } from '../services/system-model-sync.service';
import { UserModelSyncService } from '../services/user-model-sync.service';

/**
 * Script để chạy performance benchmark cho Model Auto-Discovery
 */
async function runPerformanceBenchmark() {
  console.log('🚀 Starting Model Auto-Discovery Performance Benchmark...\n');

  try {
    // Initialize NestJS application
    const app = await NestFactory.createApplicationContext(AppModule);
    
    // Get services
    const performanceMonitor = app.get(PerformanceMonitorService);
    const modelDiscovery = app.get(ModelDiscoveryService);
    const systemModelSync = app.get(SystemModelSyncService);
    const userModelSync = app.get(UserModelSyncService);

    console.log('✅ Application context initialized\n');

    // Test scenarios
    const testScenarios = [
      { modelCounts: [10, 25, 50], provider: 'openai', description: 'Small scale test' },
      { modelCounts: [100, 200], provider: 'openai', description: 'Medium scale test' },
      { modelCounts: [500], provider: 'openai', description: 'Large scale test' }
    ];

    for (const scenario of testScenarios) {
      console.log(`📊 Running ${scenario.description} with ${scenario.provider}...`);
      
      // Run benchmark
      const results = await performanceMonitor.runSyncModelsBenchmark(
        scenario.modelCounts,
        scenario.provider
      );

      // Generate and display report
      const report = performanceMonitor.generatePerformanceReport(results);
      console.log(report);

      // Wait between scenarios
      await new Promise(resolve => setTimeout(resolve, 2000));
    }

    // Test real ModelDiscoveryService performance
    console.log('🔬 Testing Real ModelDiscoveryService Performance...\n');
    await testRealModelDiscovery(performanceMonitor, modelDiscovery);

    // Test SystemModelSyncService performance
    console.log('🔬 Testing SystemModelSyncService Performance...\n');
    await testSystemModelSync(performanceMonitor, systemModelSync);

    console.log('✅ Performance benchmark completed successfully!');

  } catch (error) {
    console.error('❌ Benchmark failed:', error.message);
    console.error(error.stack);
  }
}

/**
 * Test real ModelDiscoveryService performance
 */
async function testRealModelDiscovery(
  monitor: PerformanceMonitorService,
  service: ModelDiscoveryService
) {
  const operationId = 'real_model_discovery_test';
  
  try {
    // Start monitoring
    monitor.startMonitoring(operationId, {
      testType: 'real_model_discovery',
      service: 'ModelDiscoveryService'
    });

    // Mock provider models (simulate API response)
    const mockModels = generateMockModels(50);
    
    console.log(`Testing with ${mockModels.length} mock models...`);

    // Test discoverSystemModels
    const systemResult = await service.discoverSystemModels(
      'mock-encrypted-key',
      'openai' as any,
      'test-key-id'
    );

    // Update metrics
    monitor.updateMetrics(operationId, {
      modelsProcessed: mockModels.length,
      newModelsCreated: systemResult.newModelsCreated,
      patternsMatched: systemResult.modelsMatched,
      mappingsCreated: systemResult.mappingsCreated
    });

    // Stop monitoring
    const metrics = monitor.stopMonitoring(operationId);
    
    if (metrics) {
      console.log('📈 Real ModelDiscoveryService Results:');
      console.log(`   Duration: ${metrics.duration}ms`);
      console.log(`   Models Processed: ${metrics.modelsProcessed}`);
      console.log(`   New Models: ${metrics.newModelsCreated}`);
      console.log(`   Mappings Created: ${metrics.mappingsCreated}`);
      console.log(`   Memory Used: ${(metrics.peakMemory - metrics.memoryBefore).toFixed(2)}MB`);
      console.log(`   Throughput: ${(metrics.modelsProcessed / (metrics.duration / 1000)).toFixed(2)} models/sec\n`);
    }

  } catch (error) {
    monitor.addError(operationId, error.message);
    console.error('❌ Real ModelDiscovery test failed:', error.message);
  }
}

/**
 * Test SystemModelSyncService performance
 */
async function testSystemModelSync(
  monitor: PerformanceMonitorService,
  service: SystemModelSyncService
) {
  const operationId = 'system_model_sync_test';
  
  try {
    // Start monitoring
    monitor.startMonitoring(operationId, {
      testType: 'system_model_sync',
      service: 'SystemModelSyncService'
    });

    console.log('Testing SystemModelSyncService with mock data...');

    // Mock sync request
    const mockRequest = {
      keyId: 'test-system-key-id',
      provider: 'openai' as const,
      encryptedApiKey: 'mock-encrypted-key'
    };

    // Note: This would normally call real API, but we'll simulate
    // const syncResult = await service.syncModels(mockRequest);

    // Simulate sync operation
    await simulateSystemModelSync(monitor, operationId);

    // Stop monitoring
    const metrics = monitor.stopMonitoring(operationId);
    
    if (metrics) {
      console.log('📈 SystemModelSyncService Results:');
      console.log(`   Duration: ${metrics.duration}ms`);
      console.log(`   Models Processed: ${metrics.modelsProcessed}`);
      console.log(`   Memory Used: ${(metrics.peakMemory - metrics.memoryBefore).toFixed(2)}MB`);
      console.log(`   DB Queries: ${metrics.databaseQueries}`);
      console.log(`   Errors: ${metrics.errors.length}\n`);
    }

  } catch (error) {
    monitor.addError(operationId, error.message);
    console.error('❌ SystemModelSync test failed:', error.message);
  }
}

/**
 * Generate mock models for testing
 */
function generateMockModels(count: number): Array<{ id: string; name: string; description?: string }> {
  const models: Array<{ id: string; name: string; description?: string }> = [];
  const modelTypes = ['gpt', 'claude', 'llama', 'gemini', 'mistral'];
  const versions = ['3.5', '4', '4-turbo', '2', '7b', '13b', '70b'];

  for (let i = 0; i < count; i++) {
    const type = modelTypes[i % modelTypes.length];
    const version = versions[i % versions.length];

    models.push({
      id: `${type}-${version}-${i}`,
      name: `${type}-${version}`,
      description: `Mock model ${type} version ${version}`
    });
  }

  return models;
}

/**
 * Simulate SystemModelSync operation
 */
async function simulateSystemModelSync(
  monitor: PerformanceMonitorService,
  operationId: string
): Promise<void> {
  const modelCount = 75;
  
  // Simulate fetching models from provider
  await new Promise(resolve => setTimeout(resolve, 200)); // API call delay
  monitor.incrementQueryCount(operationId, 1); // API call
  
  // Simulate pattern matching
  for (let i = 0; i < modelCount; i++) {
    await new Promise(resolve => setTimeout(resolve, 2)); // Pattern matching delay
    monitor.incrementQueryCount(operationId, 1); // Pattern lookup query
  }
  
  // Simulate bulk model operations
  await new Promise(resolve => setTimeout(resolve, 150)); // Bulk DB operation
  monitor.incrementQueryCount(operationId, 3); // Bulk operations
  
  // Simulate mapping creation
  await new Promise(resolve => setTimeout(resolve, 100)); // Mapping creation
  monitor.incrementQueryCount(operationId, 2); // Mapping queries
  
  // Update final metrics
  monitor.updateMetrics(operationId, {
    modelsProcessed: modelCount,
    newModelsCreated: Math.floor(modelCount * 0.6),
    patternsMatched: modelCount,
    mappingsCreated: modelCount
  });
}

/**
 * Main execution
 */
if (require.main === module) {
  runPerformanceBenchmark()
    .then(() => {
      console.log('🎉 Benchmark completed!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Benchmark failed:', error);
      process.exit(1);
    });
}

export { runPerformanceBenchmark };
