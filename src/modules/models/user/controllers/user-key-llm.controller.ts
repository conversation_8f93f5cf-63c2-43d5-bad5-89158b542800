import { SWAGGER_API_TAGS } from '@/common/swagger';
import { CurrentUser } from '@/modules/auth/decorators';
import { JwtUserGuard } from '@/modules/auth/guards';
import { ApiResponseDto } from '@common/response';
import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseUUIDPipe,
  Patch,
  Post,
  Query,
  UseGuards
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags
} from '@nestjs/swagger';
import {
  CreateUserKeyLlmDto,
  CreateUserKeyLlmResponseDto,
  UpdateUserKeyLlmResponseDto,
  ReloadModelsResponseDto,
  UpdateUserKeyLlmDto,
  UserKeyLlmQueryDto
} from '../dto/user-key-llm';
import { UserKeyLlmService } from '../services';

/**
 * Controller xử lý API cho User Key LLM
 */
@ApiTags(SWAGGER_API_TAGS.USER_API_KEY_MODEL)
@Controller('key-llm')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class UserKeyLlmController {
  constructor(private readonly userKeyLlmService: UserKeyLlmService) { }

  /**
   * Tạo mới user key LLM với model auto-discovery
   */
  @Post()
  @ApiOperation({
    summary: 'Tạo mới user key LLM với model auto-discovery',
    description: 'Tự động discovery models từ provider sau khi tạo key thành công.'
  })
  @ApiResponse({
    status: 201,
    description: 'Tạo mới user key LLM thành công với thông tin model discovery',
    type: CreateUserKeyLlmResponseDto
  })
  create(
    @Body() createDto: CreateUserKeyLlmDto,
    @CurrentUser('id') userId: number
  ): Promise<ApiResponseDto<CreateUserKeyLlmResponseDto>> {
    return this.userKeyLlmService.create(userId, createDto);
  }

  /**
   * Lấy danh sách user key LLM có phân trang
   */
  @Get()
  @ApiOperation({
    summary: 'Lấy danh sách user key LLM có phân trang',
    description: 'API này hỗ trợ tìm kiếm theo tên key, phân trang và sắp xếp'
  })
  @ApiResponse({
    status: 200,
    description: 'Danh sách user key LLM',
    type: ApiResponseDto
  })
  findAll(
    @CurrentUser('id') userId: number,
    @Query() queryDto: UserKeyLlmQueryDto
  ) {
    return this.userKeyLlmService.findAll(userId, queryDto);
  }

  /**
   * Cập nhật user key LLM với model auto-discovery (nếu có thay đổi API key)
   */
  @Patch(':id')
  @ApiOperation({
    summary: 'Cập nhật user key LLM với model auto-discovery',
    description: 'Nếu cập nhật API key, sẽ tự động discovery models mới từ provider.'
  })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật user key LLM thành công với thông tin model discovery',
    type: UpdateUserKeyLlmResponseDto
  })
  update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateDto: UpdateUserKeyLlmDto,
    @CurrentUser('id') userId: number
  ): Promise<ApiResponseDto<UpdateUserKeyLlmResponseDto>> {
    return this.userKeyLlmService.update(userId, id, updateDto);
  }

  /**
   * Reload models từ user key LLM
   */
  @Post(':id/reload-models')
  @ApiOperation({
    summary: 'Reload models từ user key LLM',
    description: 'Thực hiện lại discovery models từ provider cho key cụ thể.'
  })
  @ApiResponse({
    status: 200,
    description: 'Reload models thành công',
    type: ReloadModelsResponseDto
  })
  reloadModels(
    @Param('id', ParseUUIDPipe) keyId: string,
    @CurrentUser('id') userId: number
  ): Promise<ApiResponseDto<ReloadModelsResponseDto>> {
    return this.userKeyLlmService.reloadModels(userId, keyId);
  }

  /**
   * Xóa user key LLM
   */
  @Delete(':id')
  @ApiOperation({ summary: 'Xóa user key LLM và clear model mappings' })
  @ApiResponse({
    status: 200,
    description: 'Xóa user key LLM thành công',
    type: ApiResponseDto
  })
  remove(
    @Param('id', ParseUUIDPipe) id: string,
    @CurrentUser('id') userId: number
  ) {
    return this.userKeyLlmService.remove(userId, id);
  }
}
