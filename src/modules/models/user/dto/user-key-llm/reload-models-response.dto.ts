import { ApiProperty } from '@nestjs/swagger';
import { ModelDiscoveryInfo } from '../../../interfaces/model-discovery-info.interface';

/**
 * DTO cho response khi reload models từ user key LLM
 */
export class ReloadModelsResponseDto {
  @ApiProperty({
    description: 'ID của user key LLM',
    example: 'uuid-string'
  })
  keyId: string;

  @ApiProperty({
    description: 'Provider của key',
    example: 'openai'
  })
  provider: string;

  @ApiProperty({
    description: 'ID của user',
    example: 123
  })
  userId: number;

  @ApiProperty({
    description: 'Thông tin model discovery',
    example: {
      totalModelsFound: 15,
      modelsMatched: 12,
      newModelsCreated: 2,
      existingModelsFound: 10,
      mappingsCreated: 12,
      discoveryTime: 1640995200000,
      success: true,
      message: 'Reload thành công: 2 models mới, 10 models đã tồn tại',
      errors: []
    }
  })
  modelDiscovery: ModelDiscoveryInfo;
}
