import { ApiProperty } from '@nestjs/swagger';
import { ModelDiscoveryInfo } from '../../../interfaces/model-discovery-info.interface';

/**
 * DTO cho response khi cập nhật user key LLM
 */
export class UpdateUserKeyLlmResponseDto {
  @ApiProperty({
    description: 'ID của user key LLM được cập nhật',
    example: 'uuid-string'
  })
  id: string;

  @ApiProperty({
    description: 'Lỗi connection test nếu có',
    example: 'API key không hợp lệ',
    required: false
  })
  connectionError?: string;

  @ApiProperty({
    description: 'Thông tin model discovery (chỉ có khi cập nhật API key)',
    required: false,
    example: {
      totalModelsFound: 15,
      modelsMatched: 12,
      newModelsCreated: 3,
      existingModelsFound: 9,
      mappingsCreated: 12,
      discoveryTime: 1640995200000,
      success: true,
      message: 'Discovery thành công: 3 models mới, 9 models đã tồn tại',
      errors: []
    }
  })
  modelDiscovery?: ModelDiscoveryInfo;
}
