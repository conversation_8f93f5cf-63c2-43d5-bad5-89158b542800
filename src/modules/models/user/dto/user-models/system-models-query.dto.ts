import { ProviderEnum } from '@/modules/models/constants/provider.enum';
import { QueryDto } from '@common/dto';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum } from 'class-validator';

/**
 * DTO cho việc truy vấn danh sách system models
 */
export class SystemModelsQueryDto extends QueryDto {
  /**
   * Lọc theo nhà cung cấp
   */
  @ApiPropertyOptional({
    description: 'Lọc theo nhà cung cấp',
    enum: ProviderEnum,
    example: ProviderEnum.OPENAI,
  })
  @IsEnum(ProviderEnum, { message: 'Provider phải là một trong các giá trị hợp lệ' })
  provider: ProviderEnum;
}
