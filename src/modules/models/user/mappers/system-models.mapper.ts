import { SystemModelsResponseDto, ModelPricingInterface } from '../dto/user-models/system-models-response.dto';
import { SystemModelWithRegistry } from '../../repositories/system-models.repository';

/**
 * Mapper cho System Models
 */
export class SystemModelsMapper {
  /**
   * Convert SystemModelWithRegistry entity sang SystemModelsResponseDto
   * @param entity SystemModelWithRegistry entity
   * @returns SystemModelsResponseDto
   */
  static toResponseDto(entity: SystemModelWithRegistry): SystemModelsResponseDto {
    return {
      id: entity.id,
      modelId: entity.modelId,
      provider: entity.provider,
      modelNamePattern: entity.modelNamePattern,
      inputModalities: Array.isArray(entity.inputModalities) ? entity.inputModalities : [],
      outputModalities: Array.isArray(entity.outputModalities) ? entity.outputModalities : [],
      samplingParameters: Array.isArray(entity.samplingParameters) ? entity.samplingParameters : [],
      features: Array.isArray(entity.features) ? entity.features : [],
      basePricing: entity.basePricing || { inputRate: 0, outputRate: 0 },
      fineTunePricing: entity.fineTunePricing || { inputRate: 0, outputRate: 0 },
      trainingPricing: entity.trainingPricing || 0,
    };
  }

  /**
   * Convert array của SystemModelWithRegistry entities sang array của SystemModelsResponseDto
   * @param entities Array của SystemModelWithRegistry entities
   * @returns Array của SystemModelsResponseDto
   */
  static toResponseDtoList(entities: SystemModelWithRegistry[]): SystemModelsResponseDto[] {
    if (!entities || !Array.isArray(entities)) {
      return [];
    }

    return entities
      .map(entity => this.toResponseDto(entity))
      .filter(dto => dto !== null);
  }
}
